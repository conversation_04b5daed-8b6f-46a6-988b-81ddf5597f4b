// Setup tests before environment is loaded
// This file runs before the test framework is installed

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.REACT_APP_API_URL = 'http://localhost:8000';
process.env.REACT_APP_WS_URL = 'ws://localhost:8000';
process.env.REACT_APP_ENABLE_WEBSOCKET = 'true';
process.env.REACT_APP_ENABLE_AI_SUGGESTIONS = 'true';
process.env.REACT_APP_ENABLE_TEMPLATES = 'true';
process.env.REACT_APP_ENABLE_COLLABORATION = 'true';
process.env.REACT_APP_ENABLE_CODE_EXPORT = 'true';

// Global test configuration
global.IS_REACT_ACT_ENVIRONMENT = true;

// Mock global objects that might not be available in the test environment
if (typeof global.URL !== 'undefined') {
  global.URL.createObjectURL = jest.fn();
  global.URL.revokeObjectURL = jest.fn();
} else {
  global.URL = {
    createObjectURL: jest.fn(),
    revokeObjectURL: jest.fn()
  };
}

// Mock IntersectionObserver
class MockIntersectionObserver {
  constructor(callback) {
    this.callback = callback;
    this.elements = new Set();
    this.mockEntries = [];
  }

  observe(element) {
    this.elements.add(element);
  }

  unobserve(element) {
    this.elements.delete(element);
  }

  disconnect() {
    this.elements.clear();
  }

  // Helper method to simulate intersection
  simulateIntersection(isIntersecting) {
    this.mockEntries = Array.from(this.elements).map(element => ({
      isIntersecting,
      target: element,
      intersectionRatio: isIntersecting ? 1 : 0,
      boundingClientRect: {},
      intersectionRect: {},
      rootBounds: null,
      time: Date.now(),
    }));

    this.callback(this.mockEntries, this);
  }
}

global.IntersectionObserver = MockIntersectionObserver;

// Mock ResizeObserver
class MockResizeObserver {
  constructor(callback) {
    this.callback = callback;
    this.elements = new Set();
  }

  observe(element) {
    this.elements.add(element);
  }

  unobserve(element) {
    this.elements.delete(element);
  }

  disconnect() {
    this.elements.clear();
  }
}

global.ResizeObserver = MockResizeObserver;

// Mock WebSocket
class MockWebSocket {
  constructor(url) {
    this.url = url;
    this.readyState = 0; // CONNECTING
    this.CONNECTING = 0;
    this.OPEN = 1;
    this.CLOSING = 2;
    this.CLOSED = 3;

    // Event handlers
    this.onopen = null;
    this.onmessage = null;
    this.onclose = null;
    this.onerror = null;

    // Auto connect after a short delay
    setTimeout(() => {
      this.readyState = 1; // OPEN
      if (this.onopen) {
        this.onopen({ target: this });
      }
    }, 50);
  }

  send(data) {
    // Mock sending data
    return true;
  }

  close(code = 1000, reason = '') {
    this.readyState = 3; // CLOSED
    if (this.onclose) {
      this.onclose({
        code,
        reason,
        wasClean: true,
        target: this
      });
    }
  }

  // Helper method to simulate receiving a message
  mockReceiveMessage(data) {
    if (this.onmessage) {
      this.onmessage({ data, target: this });
    }
  }

  // Helper method to simulate an error
  mockError(error) {
    if (this.onerror) {
      this.onerror({ error, target: this });
    }
  }
}

global.WebSocket = MockWebSocket;

// Mock performance API
if (!global.performance) {
  global.performance = {
    now: jest.fn(() => Date.now()),
    mark: jest.fn(),
    measure: jest.fn(),
    getEntriesByName: jest.fn(() => []),
    getEntriesByType: jest.fn(() => []),
    clearMarks: jest.fn(),
    clearMeasures: jest.fn(),
    memory: {
      jsHeapSizeLimit: 2147483648,
      totalJSHeapSize: 50000000,
      usedJSHeapSize: 25000000
    }
  };
}

// Mock requestIdleCallback
global.requestIdleCallback = (callback) => {
  return setTimeout(() => {
    callback({
      didTimeout: false,
      timeRemaining: () => 50
    });
  }, 1);
};

global.cancelIdleCallback = (id) => {
  clearTimeout(id);
};

// Mock localStorage
const localStorageMock = (function () {
  let store = {};
  return {
    getItem: jest.fn(key => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn(key => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
    key: jest.fn(index => {
      return Object.keys(store)[index] || null;
    }),
    get length() {
      return Object.keys(store).length;
    }
  };
})();

// Only define window properties if window exists
if (typeof window !== 'undefined') {
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock
  });

  // Mock matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(), // deprecated
      removeListener: jest.fn(), // deprecated
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });
}

// Global test utilities
global.testUtils = {
  // Helper to wait for async operations
  waitFor: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),

  // Helper to create mock events
  createMockEvent: (type, properties = {}) => ({
    type,
    preventDefault: jest.fn(),
    stopPropagation: jest.fn(),
    target: { value: '' },
    ...properties,
  }),

  // Helper to create mock drag events
  createMockDragEvent: (type, dataTransfer = {}) => ({
    type,
    preventDefault: jest.fn(),
    stopPropagation: jest.fn(),
    dataTransfer: {
      setData: jest.fn(),
      getData: jest.fn(),
      clearData: jest.fn(),
      dropEffect: 'none',
      effectAllowed: 'all',
      files: [],
      items: [],
      types: [],
      ...dataTransfer,
    },
  }),

  // Helper to create mock WebSocket messages
  createMockWebSocketMessage: (type, data = {}) => ({
    type,
    timestamp: Date.now(),
    ...data,
  }),
};

// Mock crypto for testing
Object.defineProperty(global, 'crypto', {
  value: {
    getRandomValues: jest.fn((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    }),
    randomUUID: jest.fn(() => 'mock-uuid-' + Math.random().toString(36).substr(2, 9)),
  },
});

// Mock TextEncoder/TextDecoder
global.TextEncoder = class TextEncoder {
  encode(str) {
    return new Uint8Array([...str].map(char => char.charCodeAt(0)));
  }
};

global.TextDecoder = class TextDecoder {
  decode(bytes) {
    return String.fromCharCode(...bytes);
  }
};
