/**
 * UX Enhanced Property Editor
 * 
 * A comprehensive property editor with enhanced UI/UX features:
 * - Intuitive form controls with better validation
 * - Real-time preview integration
 * - Property grouping and organization
 * - Contextual help and tooltips
 * - Improved visual hierarchy
 * - Accessibility enhancements
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Typography,
  Form,
  Input,
  Select,
  Switch,
  InputNumber,
  Slider,
  ColorPicker,
  Button,
  Tabs,
  Collapse,
  Space,
  Tooltip,
  Alert,
  Badge,
  Divider,
  Card,
  Row,
  Col,
  message
} from 'antd';
import {
  InfoCircleOutlined,
  EyeOutlined,
  ReloadOutlined,
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  SettingOutlined,
  BgColorsOutlined,
  FontSizeOutlined,
  BorderOutlined,
  ExpandOutlined,
  QuestionCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { theme, a11yUtils, componentUtils } from '../../design-system';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { Panel } = Collapse;
const { TabPane } = Tabs;
const { TextArea } = Input;

// Enhanced styled components
const PropertyEditorContainer = styled.div`
  background: ${theme.colors.background.paper};
  border-radius: ${theme.borderRadius.lg};
  box-shadow: ${theme.shadows.md};
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid ${theme.colors.border.light};
`;

const PropertyHeader = styled.div`
  padding: ${theme.spacing[4]};
  background: linear-gradient(135deg, ${theme.colors.secondary.main} 0%, ${theme.colors.primary.main} 100%);
  color: ${theme.colors.background.paper};
  border-bottom: 1px solid ${theme.colors.border.light};
  
  .ant-typography {
    color: ${theme.colors.background.paper} !important;
    margin-bottom: ${theme.spacing[1]};
  }
`;

const PropertyContent = styled.div`
  flex: 1;
  overflow-y: auto;
  
  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: ${theme.colors.background.secondary};
  }
  
  &::-webkit-scrollbar-thumb {
    background: ${theme.colors.border.medium};
    border-radius: ${theme.borderRadius.full};
  }
`;

const PropertyGroup = styled(Card)`
  margin: ${theme.spacing[2]};
  border-radius: ${theme.borderRadius.md};
  
  .ant-card-head {
    background: ${theme.colors.background.tertiary};
    border-bottom: 1px solid ${theme.colors.border.light};
    padding: ${theme.spacing[2]} ${theme.spacing[3]};
    min-height: auto;
  }
  
  .ant-card-body {
    padding: ${theme.spacing[3]};
  }
`;

const PropertyField = styled.div`
  margin-bottom: ${theme.spacing[3]};
  
  .ant-form-item {
    margin-bottom: ${theme.spacing[2]};
  }
  
  .ant-form-item-label {
    padding-bottom: ${theme.spacing[1]};
    
    label {
      font-weight: ${theme.typography.fontWeight.medium};
      color: ${theme.colors.text.primary};
      font-size: ${theme.typography.fontSize.sm};
    }
  }
  
  .property-help {
    margin-top: ${theme.spacing[1]};
    font-size: ${theme.typography.fontSize.xs};
    color: ${theme.colors.text.secondary};
    line-height: ${theme.typography.lineHeight.normal};
  }
`;

const ValidationMessage = styled.div`
  margin-top: ${theme.spacing[1]};
  padding: ${theme.spacing[1]} ${theme.spacing[2]};
  border-radius: ${theme.borderRadius.sm};
  font-size: ${theme.typography.fontSize.xs};
  
  &.error {
    background: ${theme.colors.error.light};
    color: ${theme.colors.error.dark};
    border: 1px solid ${theme.colors.error.main};
  }
  
  &.warning {
    background: ${theme.colors.warning.light};
    color: ${theme.colors.warning.dark};
    border: 1px solid ${theme.colors.warning.main};
  }
  
  &.success {
    background: ${theme.colors.success.light};
    color: ${theme.colors.success.dark};
    border: 1px solid ${theme.colors.success.main};
  }
`;

const PreviewToggle = styled.div`
  position: absolute;
  top: ${theme.spacing[2]};
  right: ${theme.spacing[2]};
  z-index: 10;
`;

const QuickActions = styled.div`
  padding: ${theme.spacing[2]} ${theme.spacing[4]};
  background: ${theme.colors.background.secondary};
  border-top: 1px solid ${theme.colors.border.light};
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

// Property validation rules
const VALIDATION_RULES = {
  required: (value) => value !== undefined && value !== null && value !== '',
  minLength: (min) => (value) => !value || value.length >= min,
  maxLength: (max) => (value) => !value || value.length <= max,
  pattern: (regex) => (value) => !value || regex.test(value),
  range: (min, max) => (value) => !value || (value >= min && value <= max),
  url: (value) => !value || /^https?:\/\/.+/.test(value),
  color: (value) => !value || /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value),
};

// Property schemas with enhanced metadata
const PROPERTY_SCHEMAS = {
  text: {
    content: {
      type: 'textarea',
      label: 'Content',
      placeholder: 'Enter text content...',
      help: 'The main text content to display',
      validation: [VALIDATION_RULES.required],
      group: 'content'
    },
    type: {
      type: 'select',
      label: 'Text Type',
      options: [
        { value: 'paragraph', label: 'Paragraph' },
        { value: 'title', label: 'Title' },
        { value: 'secondary', label: 'Secondary' },
        { value: 'warning', label: 'Warning' },
        { value: 'danger', label: 'Danger' }
      ],
      defaultValue: 'paragraph',
      help: 'Choose the semantic type of text',
      group: 'appearance'
    },
    strong: {
      type: 'switch',
      label: 'Bold',
      help: 'Make text bold/strong',
      group: 'typography'
    },
    italic: {
      type: 'switch',
      label: 'Italic',
      help: 'Make text italic/emphasized',
      group: 'typography'
    },
    underline: {
      type: 'switch',
      label: 'Underline',
      help: 'Add underline decoration',
      group: 'typography'
    }
  },
  button: {
    text: {
      type: 'input',
      label: 'Button Text',
      placeholder: 'Enter button text...',
      help: 'The text displayed on the button',
      validation: [VALIDATION_RULES.required, VALIDATION_RULES.maxLength(50)],
      group: 'content'
    },
    type: {
      type: 'select',
      label: 'Button Type',
      options: [
        { value: 'default', label: 'Default' },
        { value: 'primary', label: 'Primary' },
        { value: 'dashed', label: 'Dashed' },
        { value: 'text', label: 'Text' },
        { value: 'link', label: 'Link' }
      ],
      defaultValue: 'default',
      help: 'Visual style of the button',
      group: 'appearance'
    },
    size: {
      type: 'select',
      label: 'Size',
      options: [
        { value: 'small', label: 'Small' },
        { value: 'middle', label: 'Medium' },
        { value: 'large', label: 'Large' }
      ],
      defaultValue: 'middle',
      help: 'Size of the button',
      group: 'layout'
    },
    disabled: {
      type: 'switch',
      label: 'Disabled',
      help: 'Disable button interaction',
      group: 'behavior'
    },
    action: {
      type: 'input',
      label: 'Action',
      placeholder: 'Enter action name...',
      help: 'Action to perform when clicked',
      group: 'behavior'
    }
  },
  header: {
    title: {
      type: 'input',
      label: 'Title',
      placeholder: 'Enter header title...',
      help: 'Main title text',
      validation: [VALIDATION_RULES.required],
      group: 'content'
    },
    subtitle: {
      type: 'input',
      label: 'Subtitle',
      placeholder: 'Enter subtitle...',
      help: 'Optional subtitle text',
      group: 'content'
    },
    alignment: {
      type: 'select',
      label: 'Alignment',
      options: [
        { value: 'left', label: 'Left' },
        { value: 'center', label: 'Center' },
        { value: 'right', label: 'Right' }
      ],
      defaultValue: 'center',
      help: 'Text alignment',
      group: 'layout'
    },
    background: {
      type: 'color',
      label: 'Background Color',
      help: 'Background color of the header',
      group: 'appearance'
    },
    textColor: {
      type: 'color',
      label: 'Text Color',
      help: 'Color of the text',
      group: 'appearance'
    }
  }
};

// Property groups configuration
const PROPERTY_GROUPS = {
  content: {
    title: 'Content',
    icon: <FileTextOutlined />,
    description: 'Text, images, and other content properties'
  },
  appearance: {
    title: 'Appearance',
    icon: <BgColorsOutlined />,
    description: 'Visual styling and colors'
  },
  layout: {
    title: 'Layout',
    icon: <ExpandOutlined />,
    description: 'Size, spacing, and positioning'
  },
  typography: {
    title: 'Typography',
    icon: <FontSizeOutlined />,
    description: 'Font styles and text formatting'
  },
  behavior: {
    title: 'Behavior',
    icon: <SettingOutlined />,
    description: 'Interactions and functionality'
  },
  advanced: {
    title: 'Advanced',
    icon: <SettingOutlined />,
    description: 'Custom CSS and advanced options'
  }
};

export default function UXEnhancedPropertyEditor({
  component,
  onUpdateComponent,
  onPreviewChange,
  dataSources = [],
  showPreview = true,
  enableRealTimePreview = true
}) {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('properties');
  const [expandedGroups, setExpandedGroups] = useState(['content', 'appearance']);
  const [validationErrors, setValidationErrors] = useState({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [previewEnabled, setPreviewEnabled] = useState(enableRealTimePreview);

  // Get property schema for current component type
  const propertySchema = useMemo(() => {
    return PROPERTY_SCHEMAS[component?.type] || {};
  }, [component?.type]);

  // Group properties by their group
  const groupedProperties = useMemo(() => {
    const groups = {};
    Object.entries(propertySchema).forEach(([key, schema]) => {
      const groupKey = schema.group || 'advanced';
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push({ key, ...schema });
    });
    return groups;
  }, [propertySchema]);

  // Validate a single property
  const validateProperty = useCallback((key, value, schema) => {
    if (!schema.validation) return null;

    for (const rule of schema.validation) {
      if (!rule(value)) {
        return `Invalid ${schema.label.toLowerCase()}`;
      }
    }
    return null;
  }, []);

  // Validate all properties
  const validateAllProperties = useCallback(() => {
    const errors = {};
    const values = form.getFieldsValue();

    Object.entries(propertySchema).forEach(([key, schema]) => {
      const error = validateProperty(key, values[key], schema);
      if (error) {
        errors[key] = error;
      }
    });

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [form, propertySchema, validateProperty]);

  // Handle form value changes
  const handleValuesChange = useCallback((changedValues, allValues) => {
    setHasUnsavedChanges(true);

    // Validate changed values
    const errors = { ...validationErrors };
    Object.entries(changedValues).forEach(([key, value]) => {
      const schema = propertySchema[key];
      if (schema) {
        const error = validateProperty(key, value, schema);
        if (error) {
          errors[key] = error;
        } else {
          delete errors[key];
        }
      }
    });
    setValidationErrors(errors);

    // Real-time preview update
    if (previewEnabled && onUpdateComponent) {
      onUpdateComponent(component.id, changedValues);
    }

    // Notify preview change
    if (onPreviewChange) {
      onPreviewChange(allValues);
    }
  }, [component?.id, onUpdateComponent, onPreviewChange, previewEnabled, validationErrors, propertySchema, validateProperty]);

  // Save changes
  const handleSave = useCallback(() => {
    if (validateAllProperties()) {
      const values = form.getFieldsValue();
      onUpdateComponent(component.id, values);
      setHasUnsavedChanges(false);
      message.success('Properties saved successfully');
    } else {
      message.error('Please fix validation errors before saving');
    }
  }, [component?.id, form, onUpdateComponent, validateAllProperties]);

  // Reset form
  const handleReset = useCallback(() => {
    form.setFieldsValue(component.props);
    setValidationErrors({});
    setHasUnsavedChanges(false);
    message.info('Properties reset to original values');
  }, [component?.props, form]);

  // Set initial form values
  useEffect(() => {
    if (component?.props) {
      form.setFieldsValue(component.props);
      setHasUnsavedChanges(false);
      setValidationErrors({});
    }
  }, [component, form]);

  // Render property field based on type
  const renderPropertyField = useCallback((property) => {
    const { key, type, label, placeholder, help, options, defaultValue, validation } = property;
    const hasError = validationErrors[key];
    const isRequired = validation?.some(rule => rule === VALIDATION_RULES.required);

    const fieldProps = {
      name: key,
      label: (
        <Space>
          {label}
          {isRequired && <Text type="danger">*</Text>}
          {help && (
            <Tooltip title={help} placement="topLeft">
              <QuestionCircleOutlined style={{ color: theme.colors.text.tertiary }} />
            </Tooltip>
          )}
        </Space>
      ),
      validateStatus: hasError ? 'error' : '',
      help: hasError ? (
        <ValidationMessage className="error">
          <ExclamationCircleOutlined /> {hasError}
        </ValidationMessage>
      ) : help ? (
        <div className="property-help">{help}</div>
      ) : null
    };

    switch (type) {
      case 'input':
        return (
          <Form.Item {...fieldProps}>
            <Input
              placeholder={placeholder}
              style={{ borderRadius: theme.borderRadius.md }}
            />
          </Form.Item>
        );

      case 'textarea':
        return (
          <Form.Item {...fieldProps}>
            <TextArea
              rows={4}
              placeholder={placeholder}
              style={{ borderRadius: theme.borderRadius.md }}
            />
          </Form.Item>
        );

      case 'select':
        return (
          <Form.Item {...fieldProps}>
            <Select
              placeholder={placeholder}
              style={{ borderRadius: theme.borderRadius.md }}
            >
              {options?.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        );

      case 'switch':
        return (
          <Form.Item {...fieldProps} valuePropName="checked">
            <Switch />
          </Form.Item>
        );

      case 'number':
        return (
          <Form.Item {...fieldProps}>
            <InputNumber
              placeholder={placeholder}
              style={{ width: '100%', borderRadius: theme.borderRadius.md }}
            />
          </Form.Item>
        );

      case 'slider':
        return (
          <Form.Item {...fieldProps}>
            <Slider />
          </Form.Item>
        );

      case 'color':
        return (
          <Form.Item {...fieldProps}>
            <ColorPicker />
          </Form.Item>
        );

      default:
        return (
          <Form.Item {...fieldProps}>
            <Input placeholder={placeholder} />
          </Form.Item>
        );
    }
  }, [validationErrors]);

  if (!component) {
    return (
      <PropertyEditorContainer>
        <div style={{ 
          padding: theme.spacing[8], 
          textAlign: 'center',
          color: theme.colors.text.secondary 
        }}>
          <SettingOutlined style={{ fontSize: 48, marginBottom: theme.spacing[4] }} />
          <Title level={4} style={{ color: theme.colors.text.secondary }}>
            No Component Selected
          </Title>
          <Paragraph>
            Select a component from the canvas to edit its properties
          </Paragraph>
        </div>
      </PropertyEditorContainer>
    );
  }

  return (
    <PropertyEditorContainer>
      <PropertyHeader>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={5} style={{ margin: 0, color: 'white' }}>
              Properties
            </Title>
            <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
              {component.type} component
            </Text>
          </div>
          
          {showPreview && (
            <PreviewToggle>
              <Tooltip title={previewEnabled ? 'Disable real-time preview' : 'Enable real-time preview'}>
                <Switch
                  checked={previewEnabled}
                  onChange={setPreviewEnabled}
                  checkedChildren={<EyeOutlined />}
                  unCheckedChildren={<EyeOutlined />}
                  size="small"
                />
              </Tooltip>
            </PreviewToggle>
          )}
        </div>
        
        {hasUnsavedChanges && (
          <Alert
            message="You have unsaved changes"
            type="warning"
            showIcon
            style={{ 
              marginTop: theme.spacing[2],
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              color: 'white'
            }}
          />
        )}
      </PropertyHeader>

      <PropertyContent>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          style={{ padding: theme.spacing[2] }}
        >
          <TabPane tab="Properties" key="properties">
            <Form
              form={form}
              layout="vertical"
              onValuesChange={handleValuesChange}
              initialValues={component.props}
            >
              <Collapse
                activeKey={expandedGroups}
                onChange={setExpandedGroups}
                ghost
              >
                {Object.entries(groupedProperties).map(([groupKey, properties]) => {
                  const groupConfig = PROPERTY_GROUPS[groupKey];
                  if (!groupConfig || properties.length === 0) return null;

                  return (
                    <Panel
                      header={
                        <Space>
                          {groupConfig.icon}
                          <span>{groupConfig.title}</span>
                          <Badge count={properties.length} size="small" />
                        </Space>
                      }
                      key={groupKey}
                    >
                      <div style={{ padding: theme.spacing[2] }}>
                        <Text type="secondary" style={{ fontSize: theme.typography.fontSize.xs }}>
                          {groupConfig.description}
                        </Text>
                        <Divider style={{ margin: `${theme.spacing[2]} 0` }} />
                        
                        {properties.map(property => (
                          <PropertyField key={property.key}>
                            {renderPropertyField(property)}
                          </PropertyField>
                        ))}
                      </div>
                    </Panel>
                  );
                })}
              </Collapse>
            </Form>
          </TabPane>

          <TabPane tab="Style" key="style">
            <div style={{ padding: theme.spacing[2] }}>
              <Alert
                message="Style Editor"
                description="Advanced styling options will be available in the next update."
                type="info"
                showIcon
              />
            </div>
          </TabPane>

          <TabPane tab="Advanced" key="advanced">
            <div style={{ padding: theme.spacing[2] }}>
              <Alert
                message="Advanced Options"
                description="Custom CSS and JavaScript options will be available in the next update."
                type="info"
                showIcon
              />
            </div>
          </TabPane>
        </Tabs>
      </PropertyContent>

      <QuickActions>
        <Space>
          <Button
            icon={<UndoOutlined />}
            onClick={handleReset}
            disabled={!hasUnsavedChanges}
            size="small"
          >
            Reset
          </Button>
          
          <Button
            icon={<ReloadOutlined />}
            onClick={() => form.resetFields()}
            size="small"
          >
            Refresh
          </Button>
        </Space>

        <Space>
          <Text style={{ fontSize: theme.typography.fontSize.xs, color: theme.colors.text.secondary }}>
            {Object.keys(validationErrors).length > 0 && (
              <span style={{ color: theme.colors.error.main }}>
                {Object.keys(validationErrors).length} error(s)
              </span>
            )}
            {Object.keys(validationErrors).length === 0 && hasUnsavedChanges && (
              <span style={{ color: theme.colors.success.main }}>
                <CheckCircleOutlined /> Ready to save
              </span>
            )}
          </Text>
          
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSave}
            disabled={Object.keys(validationErrors).length > 0 || !hasUnsavedChanges}
            size="small"
          >
            Save
          </Button>
        </Space>
      </QuickActions>
    </PropertyEditorContainer>
  );
}
