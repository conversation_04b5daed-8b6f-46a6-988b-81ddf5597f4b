"""
Test settings for the App Builder 201 project.
"""

from .settings import *

# Use in-memory SQLite database for testing
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }
}

# Use in-memory cache for testing
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
    }
}

# Use in-memory channel layer for testing
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels.layers.InMemoryChannelLayer',
    }
}

# Disable logging during tests
LOGGING = {
    'version': 1,
    'disable_existing_loggers': True,
    'handlers': {
        'null': {
            'class': 'logging.NullHandler',
        },
    },
    'loggers': {
        '': {
            'handlers': ['null'],
            'level': 'CRITICAL',
        },
    },
}

# Disable CSRF protection during tests
MIDDLEWARE = [m for m in MIDDLEWARE if 'CsrfViewMiddleware' not in m]

# Use the test authentication middleware
ASGI_APPLICATION = 'app_builder_201.test_asgi.application'

# Set a fixed secret key for testing
SECRET_KEY = 'test-secret-key'

# Disable debug mode
DEBUG = False

# Allow all hosts
ALLOWED_HOSTS = ['*']

# Use the test authentication middleware
if 'my_app.middleware.WebSocketAuthMiddleware' in ASGI_APPLICATION:
    WebSocketAuthMiddleware = 'my_app.middleware.TokenAuthMiddleware'

# Test environment configuration
TESTING = True

# Use faster password hasher for tests
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]

# Disable migrations for faster test setup
class DisableMigrations:
    def __contains__(self, item):
        return True

    def __getitem__(self, item):
        return None

MIGRATION_MODULES = DisableMigrations()

# Email backend for tests
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Static and media files for tests
import tempfile
import os

STATIC_ROOT = os.path.join(tempfile.gettempdir(), 'static_test')
MEDIA_ROOT = os.path.join(tempfile.gettempdir(), 'media_test')

# CORS settings for tests
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

# Security settings for tests
CSRF_COOKIE_SECURE = False
SESSION_COOKIE_SECURE = False

# Test-specific feature flags
FEATURE_FLAGS = {
    'AI_SUGGESTIONS': True,
    'TEMPLATES': True,
    'COLLABORATION': True,
    'CODE_EXPORT': True,
    'WEBSOCKET': True,
    'PERFORMANCE_MONITORING': False,
    'SECURITY_SCANNING': False,
}

# Test user credentials
TEST_USER_EMAIL = '<EMAIL>'
TEST_USER_PASSWORD = 'testpassword123'
TEST_SUPERUSER_EMAIL = '<EMAIL>'
TEST_SUPERUSER_PASSWORD = 'adminpassword123'

# API settings for tests
API_RATE_LIMIT = '1000/hour'

# WebSocket settings for tests
WEBSOCKET_HEARTBEAT_INTERVAL = 1
WEBSOCKET_RECONNECT_ATTEMPTS = 3
WEBSOCKET_RECONNECT_DELAY = 0.1

# Test reports directory
TEST_REPORTS_DIR = os.path.join(BASE_DIR, 'reports')
os.makedirs(TEST_REPORTS_DIR, exist_ok=True)
os.makedirs(STATIC_ROOT, exist_ok=True)
os.makedirs(MEDIA_ROOT, exist_ok=True)

# Mock external services in tests
MOCK_EXTERNAL_SERVICES = True
OPENAI_API_KEY = 'test-api-key'

# Disable rate limiting in tests
RATELIMIT_ENABLE = False

# Test environment variables
os.environ.setdefault('TESTING', 'True')
