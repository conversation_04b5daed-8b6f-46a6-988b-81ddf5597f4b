# backend/my_app/views.py
from django.http import JsonResponse, HttpResponse
from django.shortcuts import render
from .models import App
import json
import os
import logging
from django.views.decorators.csrf import csrf_exempt
from django.middleware.csrf import get_token
from rest_framework.decorators import api_view, permission_classes
from rest_framework import permissions
from core.app_logic import AppBuilder, AIPlugin, TutorialAIPlugin, AILayoutSuggestionsEngine, AIComponentCombinationEngine
from .validators import validate_json_request, validate_app_data, validate_image_request, validate_ai_suggestions_request
from .error_handling import error_response, handle_exception, api_key_error
from .security import add_security_headers, sanitize_request
from .rate_limiting import rate_limit
from .api_docs import document_api, response_example
from django.utils import timezone
from datetime import datetime

# Set up logger
logger = logging.getLogger(__name__)

# Try to import openai, but don't fail if it's not available
try:
    import openai
except ImportError:
    openai = None

def get_app_data(request):
    app = App.objects.first()
    if app:
        return JsonResponse(json.loads(app.app_data), safe=False)
    else:
        return JsonResponse({"error": "No app data found"}, status=404)

def save_app_data(request):
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            app = App.objects.first()
            if app:
                app.app_data = json.dumps(data)
                app.save()
                return JsonResponse({"message": "App data updated", "app_data": data})
            else:
                app = App.objects.create(name="New App", app_data=json.dumps(data))
                return JsonResponse({"message": "App data created", "app_data": data})
        except json.JSONDecodeError:
            return JsonResponse({"error": "Invalid JSON data"}, status=400)
    else:
        return JsonResponse({"error": "Method not allowed"}, status=405)

def export_app_data(request):
    format_type = request.GET.get('format', 'json')
    app = App.objects.first()
    if app:
        app_data = json.loads(app.app_data)
        app_builder = AppBuilder()

        # Populate the app builder with the stored data
        for component in app_data.get('components', []):
            app_builder.add_component(component.get('type'), component.get('props', {}))

        for layout in app_data.get('layouts', []):
            app_builder.add_layout(layout.get('type'), layout.get('components', []), layout.get('styles', {}))

        for selector, style in app_data.get('styles', {}).items():
            app_builder.add_style(selector, style)

        for key, value in app_data.get('data', {}).items():
            app_builder.add_data(key, value)

        # Export in the requested format
        result = app_builder.export(format=format_type)

        if format_type == 'json':
            return JsonResponse(result, safe=False)
        else:
            return JsonResponse({"code": result})
    else:
        return JsonResponse({"error": "No app data found"}, status=404)

def get_plugin_props(request, plugin):
    if plugin == 'AIPlugin':
        return JsonResponse({"prompt": "", "apiKey": ""})
    else:
        return JsonResponse({"error": "Plugin not found"}, status=404)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])  # Require authentication for AI features
@handle_exception
def generate_ai_suggestions(request):
    # Validate request
    is_valid, data, error_resp = validate_json_request(request, required_fields=['prompt'])
    if not is_valid:
        return error_resp

    # Validate AI suggestions request
    is_valid, error_message = validate_ai_suggestions_request(data)
    if not is_valid:
        return error_response('INVALID_FIELD', error_message, 400)

    # Get API key from environment
    api_key = os.environ.get('OPENAI_API_KEY')
    if not api_key:
        logger.error("OpenAI API key is missing")
        return api_key_error('OpenAI')

    prompt = data['prompt']
    logger.info(f"Generating AI suggestions for prompt: {prompt[:50]}...")

    # Generate suggestions
    ai_plugin = AIPlugin(api_key=api_key)
    suggestions = ai_plugin.get_suggestions(prompt)

    # Return response
    return JsonResponse({
        "suggestions": suggestions.split('\n'),
        "status": "success",
        "prompt": prompt
    })

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])  # Require authentication for AI features
@handle_exception
def generate_image(request):
    # Validate request
    is_valid, data, error_resp = validate_json_request(request, required_fields=['prompt'])
    if not is_valid:
        return error_resp

    # Validate image request
    is_valid, error_message = validate_image_request(data)
    if not is_valid:
        return error_response('INVALID_FIELD', error_message, 400)

    prompt = data['prompt']
    api_type = data.get('api', 'openai')  # Default to OpenAI

    # Get appropriate API key based on the requested API
    if api_type == 'openai':
        api_key = os.environ.get('OPENAI_API_KEY')
        if not api_key:
            logger.error("OpenAI API key is missing")
            return api_key_error('OpenAI')
    elif api_type == 'stability':
        api_key = os.environ.get('STABILITY_API_KEY')
        if not api_key:
            logger.error("Stability API key is missing")
            return api_key_error('Stability')
    else:
        api_key = None

    logger.info(f"Generating image with {api_type} API for prompt: {prompt[:50]}...")

    # Create AI plugin with the appropriate API key
    ai_plugin = AIPlugin(api_key=api_key)

    # Generate image using the specified API
    image_url = ai_plugin.generate_image(prompt, api=api_type)

    # Return response
    return JsonResponse({
        "image_url": image_url,
        "api": api_type,
        "prompt": prompt,
        "status": "success"
    })

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])  # Require authentication for AI features
@handle_exception
def generate_tutorial_response(request):
    # Validate request
    is_valid, data, error_resp = validate_json_request(request, required_fields=['prompt'])
    if not is_valid:
        return error_resp

    # Validate AI suggestions request (same validation as for generate_ai_suggestions)
    is_valid, error_message = validate_ai_suggestions_request(data)
    if not is_valid:
        return error_response('INVALID_FIELD', error_message, 400)

    prompt = data['prompt']

    # Get API key from environment
    api_key = os.environ.get('OPENAI_API_KEY')
    if not api_key:
        logger.error("OpenAI API key is missing")
        return api_key_error('OpenAI')

    logger.info(f"Generating tutorial response for prompt: {prompt[:50]}...")

    # Create tutorial plugin with the API key
    tutorial_plugin = TutorialAIPlugin(api_key=api_key)
    response = tutorial_plugin.generate_response(prompt)

    # Return response
    return JsonResponse({
        "response": response,
        "nextStep": tutorial_plugin.context['current_step'] + 1,
        "status": "success",
        "prompt": prompt
    })

@add_security_headers
@document_api(
    description="Render the index page",
    version="v1",
    authentication=None,
    permissions=[],
    rate_limiting={
        'limit': 100,
        'period': 60,
        'scope': 'ip'
    },
    response_examples=[
        response_example(200, "HTML content", "Index page")
    ]
)
def index(request):
    """Render the index page

    This endpoint renders the index page, which provides links to the API documentation
    and the Swagger UI. This is the entry point for the API.

    No authentication is required to access this endpoint.
    """
    return HttpResponse("<h1>Welcome to the App Builder API</h1><p>Check out the <a href='/api/docs/'>API documentation</a>, <a href='/swagger/'>Swagger UI</a>, or <a href='/graphql-docs/'>GraphQL documentation</a></p>")

@add_security_headers
@document_api(
    description="Render the Swagger UI page",
    version="v1",
    authentication=None,
    permissions=[],
    rate_limiting={
        'limit': 100,
        'period': 60,
        'scope': 'ip'
    },
    response_examples=[
        response_example(200, "HTML content", "Swagger UI page")
    ]
)
def swagger_ui(request):
    """Render the Swagger UI page

    This endpoint renders the Swagger UI page, which provides an interactive
    interface for exploring the API. The Swagger UI page uses the API documentation
    from the /api/docs/ endpoint to generate a user-friendly interface for testing
    and exploring the API.

    No authentication is required to access this endpoint.
    """
    return render(request, 'swagger.html')

@add_security_headers
@document_api(
    description="Render the GraphQL documentation page",
    version="v1",
    authentication=None,
    permissions=[],
    rate_limiting={
        'limit': 100,
        'period': 60,
        'scope': 'ip'
    },
    response_examples=[
        response_example(200, "HTML content", "GraphQL documentation page")
    ]
)
def graphql_docs(request):
    """Render the GraphQL documentation page

    This endpoint renders the GraphQL documentation page, which provides information
    about the GraphQL API. The GraphQL documentation page includes examples of queries
    and mutations, as well as information about the schema.

    No authentication is required to access this endpoint.
    """
    return render(request, 'graphql.html')

def login_test(request):
    """Render a test login page

    This endpoint renders a simple login form for testing authentication.
    """
    return render(request, 'login_test.html')

def custom_login(request):
    """Render a custom login page

    This endpoint renders a custom login form for testing authentication.
    """
    return render(request, 'custom_login.html')

from django.http import JsonResponse
from django.contrib.auth import authenticate, login

def custom_login_api(request):
    """API endpoint for custom login

    This endpoint handles login requests from the custom login page.
    """
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate(request, username=username, password=password)

        if user is not None:
            login(request, user)
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'error': 'Invalid credentials'})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})

def get_server_time(request):
    """Return current server time in ISO format"""
    return JsonResponse({
        'serverTime': timezone.now().isoformat(),
        'timestamp': datetime.utcnow().timestamp() * 1000  # milliseconds
    })

@csrf_exempt
@api_view(['GET'])
@permission_classes([permissions.AllowAny])
@add_security_headers
@document_api(
    description="API status endpoint",
    version="v1",
    authentication=None,
    permissions=[],
    rate_limiting={
        'limit': 100,
        'period': 60,
        'scope': 'ip'
    },
    response_examples=[
        response_example(200, {"status": "ok", "version": "1.0.0"}, "Status response")
    ]
)
def api_status(request):
    """Return API status information

    This endpoint returns basic status information about the API,
    including the version and current status.

    No authentication is required to access this endpoint.
    """
    return JsonResponse({
        'status': 'ok',
        'version': '1.0.0',
        'serverTime': timezone.now().isoformat(),
        'environment': os.environ.get('DJANGO_ENVIRONMENT', 'development'),
        'message': 'API is operational'
    })

@csrf_exempt
@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def health_check(request):
    """
    Simple health check endpoint to verify the backend is running
    """
    return JsonResponse({
        'status': 'ok',
        'timestamp': timezone.now().isoformat(),
        'service': 'backend-api'
    })

@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def get_csrf_token(request):
    """
    Get CSRF token for frontend requests
    """
    token = get_token(request)
    return JsonResponse({
        'csrfToken': token,
        'status': 'ok'
    })

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@handle_exception
def generate_layout_suggestions(request):
    """
    Generate AI-powered layout suggestions based on app structure
    """
    # Validate request
    is_valid, data, error_resp = validate_json_request(request, required_fields=['components'])
    if not is_valid:
        return error_resp

    components = data['components']
    layouts = data.get('layouts', [])
    context = data.get('context', {})

    # Get API key from environment
    api_key = os.environ.get('OPENAI_API_KEY')
    if not api_key:
        logger.warning("OpenAI API key is missing - using basic suggestions only")

    logger.info(f"Generating layout suggestions for {len(components)} components...")

    try:
        # Create layout suggestions engine
        layout_engine = AILayoutSuggestionsEngine(api_key=api_key)

        # Generate suggestions
        suggestions = layout_engine.suggest_layouts(components, layouts, context)

        # Return response
        return JsonResponse({
            "suggestions": suggestions,
            "status": "success",
            "component_count": len(components),
            "timestamp": timezone.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error generating layout suggestions: {str(e)}")
        return error_response('AI_ERROR', f'Failed to generate layout suggestions: {str(e)}', 500)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@handle_exception
def generate_component_combinations(request):
    """
    Generate AI-powered component combination suggestions
    """
    # Validate request
    is_valid, data, error_resp = validate_json_request(request, required_fields=['components'])
    if not is_valid:
        return error_resp

    components = data['components']
    selected_component = data.get('selected_component')
    context = data.get('context', {})

    # Get API key from environment
    api_key = os.environ.get('OPENAI_API_KEY')
    if not api_key:
        logger.warning("OpenAI API key is missing - using basic suggestions only")

    logger.info(f"Generating component combinations for {len(components)} components...")

    try:
        # Create component combination engine
        combination_engine = AIComponentCombinationEngine(api_key=api_key)

        # Generate suggestions
        suggestions = combination_engine.suggest_combinations(components, selected_component, context)

        # Return response
        return JsonResponse({
            "suggestions": suggestions,
            "status": "success",
            "component_count": len(components),
            "selected_component": selected_component.get('type') if selected_component else None,
            "timestamp": timezone.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error generating component combinations: {str(e)}")
        return error_response('AI_ERROR', f'Failed to generate component combinations: {str(e)}', 500)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@handle_exception
def analyze_app_structure(request):
    """
    Analyze app structure and provide insights
    """
    # Validate request
    is_valid, data, error_resp = validate_json_request(request, required_fields=['components'])
    if not is_valid:
        return error_resp

    components = data['components']
    layouts = data.get('layouts', [])

    logger.info(f"Analyzing app structure with {len(components)} components...")

    try:
        # Create layout suggestions engine for analysis
        layout_engine = AILayoutSuggestionsEngine()

        # Analyze structure
        analysis = layout_engine.analyze_app_structure(components, layouts)

        # Return response
        return JsonResponse({
            "analysis": analysis,
            "status": "success",
            "timestamp": timezone.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error analyzing app structure: {str(e)}")
        return error_response('AI_ERROR', f'Failed to analyze app structure: {str(e)}', 500)
