// Setup tests after environment is loaded
// This file runs after the test framework is installed

// Add custom matchers
require('@testing-library/jest-dom');
require('jest-axe/extend-expect');

// Extend expect with custom matchers
expect.extend({
  toBeWithinRange(received, floor, ceiling) {
    const pass = received >= floor && received <= ceiling;
    if (pass) {
      return {
        message: () =>
          `expected ${received} not to be within range ${floor} - ${ceiling}`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `expected ${received} to be within range ${floor} - ${ceiling}`,
        pass: false,
      };
    }
  },

  // Custom matcher for WebSocket messages
  toBeWebSocketMessage(received, expectedType) {
    try {
      const parsed = typeof received === 'string' ? JSON.parse(received) : received;
      const pass = parsed.type === expectedType;

      if (pass) {
        return {
          message: () => `expected message not to have type "${expectedType}"`,
          pass: true,
        };
      } else {
        return {
          message: () => `expected message to have type "${expectedType}" but got "${parsed.type}"`,
          pass: false,
        };
      }
    } catch (error) {
      return {
        message: () => `expected a valid WebSocket message but got error: ${error.message}`,
        pass: false,
      };
    }
  },
});

// Global test timeouts
jest.setTimeout(10000);

// Suppress console errors during tests
const originalConsoleError = console.error;
console.error = (...args) => {
  if (
    /Warning.*not wrapped in act/i.test(args[0]) ||
    /Warning.*ReactDOM.render is no longer supported/i.test(args[0]) ||
    /Warning.*React.createFactory/i.test(args[0])
  ) {
    return;
  }
  originalConsoleError(...args);
};

// Suppress console warnings during tests
const originalConsoleWarn = console.warn;
console.warn = (...args) => {
  if (
    /Warning.*componentWillReceiveProps has been renamed/i.test(args[0]) ||
    /Warning.*componentWillMount has been renamed/i.test(args[0])
  ) {
    return;
  }
  originalConsoleWarn(...args);
};

// Enhanced mocks for comprehensive testing
import { configure } from '@testing-library/react';

// Configure testing library
configure({ testIdAttribute: 'data-testid' });

// Mock WebSocket with more realistic behavior
class MockWebSocket {
  constructor(url) {
    this.url = url;
    this.readyState = WebSocket.CONNECTING;
    this.onopen = null;
    this.onclose = null;
    this.onmessage = null;
    this.onerror = null;

    // Simulate connection after a short delay
    setTimeout(() => {
      this.readyState = WebSocket.OPEN;
      if (this.onopen) this.onopen();
    }, 10);
  }

  send(data) {
    if (this.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket is not open');
    }
    // Mock echo for testing
    setTimeout(() => {
      if (this.onmessage) {
        this.onmessage({ data: `echo: ${data}` });
      }
    }, 5);
  }

  close() {
    this.readyState = WebSocket.CLOSED;
    if (this.onclose) this.onclose();
  }

  addEventListener(event, handler) {
    this[`on${event}`] = handler;
  }

  removeEventListener(event, handler) {
    this[`on${event}`] = null;
  }
}

// WebSocket constants
MockWebSocket.CONNECTING = 0;
MockWebSocket.OPEN = 1;
MockWebSocket.CLOSING = 2;
MockWebSocket.CLOSED = 3;

global.WebSocket = MockWebSocket;

// Enhanced fetch mock
global.fetch = jest.fn((url, options) => {
  const defaultResponse = {
    ok: true,
    status: 200,
    statusText: 'OK',
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
    blob: () => Promise.resolve(new Blob()),
    headers: new Map(),
  };

  if (url.includes('/api/test-error')) {
    return Promise.resolve({
      ...defaultResponse,
      ok: false,
      status: 500,
      statusText: 'Internal Server Error',
    });
  }

  return Promise.resolve(defaultResponse);
});

// Mock storage with event simulation
const createStorageMock = (name) => {
  let store = {};
  return {
    getItem: jest.fn((key) => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
      window.dispatchEvent(new StorageEvent('storage', {
        key,
        newValue: value,
        storageArea: global[name]
      }));
    }),
    removeItem: jest.fn((key) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
    get length() {
      return Object.keys(store).length;
    },
    key: jest.fn((index) => Object.keys(store)[index] || null),
  };
};

global.localStorage = createStorageMock('localStorage');
global.sessionStorage = createStorageMock('sessionStorage');

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => {
    const matches = {
      '(prefers-reduced-motion: reduce)': false,
      '(prefers-color-scheme: dark)': false,
      '(max-width: 768px)': false,
      '(min-width: 769px)': true,
    };

    return {
      matches: matches[query] || false,
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    };
  }),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation((callback) => ({
  observe: jest.fn((element) => {
    setTimeout(() => {
      callback([{
        target: element,
        contentRect: { width: 1024, height: 768 }
      }]);
    }, 10);
  }),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation((callback) => ({
  observe: jest.fn((element) => {
    setTimeout(() => {
      callback([{
        target: element,
        isIntersecting: true,
        intersectionRatio: 1,
      }]);
    }, 10);
  }),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock animation APIs
global.requestAnimationFrame = jest.fn((cb) => setTimeout(cb, 16));
global.cancelAnimationFrame = jest.fn((id) => clearTimeout(id));

// Mock performance API
global.performance = {
  ...global.performance,
  mark: jest.fn(),
  measure: jest.fn(),
  getEntriesByName: jest.fn(() => []),
  getEntriesByType: jest.fn(() => []),
  now: jest.fn(() => Date.now()),
};

// Mock URL APIs
global.URL.createObjectURL = jest.fn(() => 'mock-object-url');
global.URL.revokeObjectURL = jest.fn();

// Mock File APIs
global.File = class MockFile {
  constructor(bits, name, options = {}) {
    this.bits = bits;
    this.name = name;
    this.size = bits.reduce((acc, bit) => acc + bit.length, 0);
    this.type = options.type || '';
    this.lastModified = options.lastModified || Date.now();
  }
};

global.FileReader = class MockFileReader {
  constructor() {
    this.readyState = 0;
    this.result = null;
    this.error = null;
  }

  readAsText(file) {
    setTimeout(() => {
      this.readyState = 2;
      this.result = 'mock file content';
      if (this.onload) this.onload();
    }, 10);
  }

  readAsDataURL(file) {
    setTimeout(() => {
      this.readyState = 2;
      this.result = 'data:text/plain;base64,bW9jayBmaWxlIGNvbnRlbnQ=';
      if (this.onload) this.onload();
    }, 10);
  }
};
