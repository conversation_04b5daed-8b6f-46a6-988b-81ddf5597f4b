#!/bin/bash

# Wait for the database to be ready
/usr/src/app/wait-for-it.sh db:5432 --timeout=60 --strict -- echo "Database is ready"

# Check for frontend static files
echo "🔍 Checking for frontend static files..."
if [ -d "/usr/src/app/frontend/build/static" ]; then
    file_count=$(find /usr/src/app/frontend/build/static -type f | wc -l)
    echo "✅ Found $file_count static files from frontend build"
else
    echo "⚠️ Frontend static files not found at /usr/src/app/frontend/build/static"
    echo "💡 This will cause Django staticfiles warnings but won't prevent startup"
fi

# Apply migrations
python /usr/src/app/manage.py migrate

# Collect static files (this will suppress the warning)
echo "📦 Collecting static files..."
python /usr/src/app/manage.py collectstatic --noinput --clear

# Start the Django ASGI server with WebSocket support
echo "🚀 Starting Django ASGI server with WebSocket support..."
cd /usr/src/app && daphne -b 0.0.0.0 -p 8000 app_builder_201.asgi:application
