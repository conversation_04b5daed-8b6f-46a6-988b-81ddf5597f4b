services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    volumes:
      - ./backend:/usr/src/app
      - ./frontend/build:/usr/src/app/frontend/build:ro
    command: bash -c "/usr/src/app/start.sh"
    environment:
      - DJ<PERSON>GO_SETTINGS_MODULE=app_builder_201.settings
      - USE_POSTGRES=true
      - POSTGRES_DB=myapp
      - POSTGRES_USER=myappuser
      - POSTGRES_PASSWORD=myapppassword
      - POSTGRES_HOST=db
      - POSTGRES_PORT=5432
      - DJANGO_SECRET_KEY=your-secret-key-for-development
      - DJANGO_DEBUG=True
      - DJANGO_ALLOWED_HOSTS=*
      - DJ<PERSON><PERSON>O_CORS_ALLOWED_ORIGINS=http://localhost:3000,http://frontend:3000
      - DJANGO_CORS_ALLOW_CREDENTIALS=True
      - DJANGO_WEBSOCKET_ALLOWED_ORIGINS=http://localhost:3000,http://frontend:3000
      - DJANGO_LOG_LEVEL=DEBUG
      - REDIS_URL=redis://redis:6379/0
      - CACHE_URL=redis://redis:6379/1
    depends_on:
      - db
      - redis
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    mem_limit: 512m
    memswap_limit: 512m
    ports:
      - "8000:8000"
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8000/health/" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    volumes:
      - ./frontend:/app:cached
      - frontend_node_modules:/app/node_modules
    environment:
      - NODE_ENV=development
      - REACT_APP_API_BASE_URL=http://localhost:8000
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
      - REACT_APP_WS_ENDPOINT=app_builder
      - REACT_APP_BACKEND_HOST=backend
      - REACT_APP_ENV=development
      - REACT_APP_DEBUG=true
      - REACT_APP_USE_REAL_API=true
      - API_TARGET=http://backend:8000
      - REACT_APP_WS_PROXY_TARGET=http://backend:8000
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
      - FAST_REFRESH=false
      - WDS_SOCKET_HOST=localhost
      - WDS_SOCKET_PATH=/sockjs-node
      - WDS_SOCKET_PORT=3000
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:3000/" ]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 60s

  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: myapp
      POSTGRES_USER: myappuser
      POSTGRES_PASSWORD: myapppassword
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U myappuser -d myapp" ]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 5
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx/dev.conf:/etc/nginx/conf.d/default.conf:ro
    depends_on:
      backend:
        condition: service_healthy
      frontend:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: [ "CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health/" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
  redis_data:
  frontend_node_modules:
