/**
 * UX Enhanced Component Palette
 * 
 * A comprehensive component palette with enhanced UI/UX features:
 * - Improved visual hierarchy and organization
 * - Better search and filtering capabilities
 * - Enhanced drag-and-drop feedback
 * - Accessibility improvements
 * - Component preview thumbnails
 * - Smart categorization and tagging
 */

import React, { useState, useMemo, useRef, useCallback } from 'react';
import { 
  Typography, 
  Input, 
  Card, 
  Badge, 
  Space, 
  Switch, 
  Tooltip, 
  Button,
  Dropdown,
  Menu,
  Divider,
  Empty,
  Spin
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  DragOutlined,
  InfoCircleOutlined,
  EyeOutlined,
  DownOutlined,
  RobotOutlined,
  ThunderboltOutlined,
  BulbOutlined,
  StarOutlined,
  TagsOutlined,
  AppstoreOutlined,
  LayoutOutlined,
  FormOutlined,
  TableOutlined,
  BarChartOutlined,
  PictureOutlined,
  FontSizeOutlined,
  OrderedListOutlined,
  CheckSquareOutlined,
  CalendarOutlined,
  SlidersFilled,
  FileTextOutlined,
  CreditCardOutlined,
  BarsOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { theme, a11yUtils, animationUtils } from '../../design-system';
import useAIDesignSuggestions from '../../hooks/useAIDesignSuggestions';
import { ComponentCombinationsList } from '../ai/ComponentCombinationCard';

const { Search } = Input;
const { Text, Title } = Typography;

// Enhanced styled components using the design system
const PaletteContainer = styled.div`
  background: ${theme.colors.background.paper};
  border-radius: ${theme.borderRadius.lg};
  box-shadow: ${theme.shadows.lg};
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid ${theme.colors.border.light};
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    border: 2px solid ${theme.colors.border.dark};
  }
`;

const PaletteHeader = styled.div`
  padding: ${theme.spacing[4]};
  background: linear-gradient(135deg, ${theme.colors.primary.main} 0%, ${theme.colors.accent.main} 100%);
  color: ${theme.colors.primary.contrastText};
  border-bottom: 1px solid ${theme.colors.border.light};
  
  .ant-typography {
    color: ${theme.colors.primary.contrastText} !important;
    margin-bottom: ${theme.spacing[2]};
  }
`;

const SearchContainer = styled.div`
  padding: ${theme.spacing[3]} ${theme.spacing[4]};
  background: ${theme.colors.background.secondary};
  border-bottom: 1px solid ${theme.colors.border.light};
  
  .ant-input-affix-wrapper {
    border-radius: ${theme.borderRadius.md};
    border: 1px solid ${theme.colors.border.default};
    transition: ${theme.transitions.default};
    
    &:focus-within {
      border-color: ${theme.colors.primary.main};
      box-shadow: ${theme.shadows.focus};
    }
  }
`;

const FilterControls = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: ${theme.spacing[2]};
  flex-wrap: wrap;
  gap: ${theme.spacing[2]};
`;

const ComponentCard = styled(Card)`
  margin: ${theme.spacing[1]};
  cursor: grab;
  transition: ${theme.transitions.default};
  border: 2px solid transparent;
  background: ${props => props.isDragging ? theme.colors.primary.light : theme.colors.background.paper};
  border-radius: ${theme.borderRadius.md};
  position: relative;
  min-height: 120px;
  
  /* Ensure minimum touch target size for accessibility */
  min-width: ${theme.accessibility.minTouchTarget.width};
  min-height: ${theme.accessibility.minTouchTarget.height};
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: ${theme.shadows.lg};
    border-color: ${theme.colors.primary.main};
    
    .component-preview {
      opacity: 1;
      transform: scale(1.05);
    }
  }
  
  &:active {
    cursor: grabbing;
    transform: scale(0.98);
  }
  
  &:focus {
    ${a11yUtils.focusRing()};
  }
  
  .ant-card-body {
    padding: ${theme.spacing[3]};
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    transition: none;
    
    &:hover {
      transform: none;
    }
    
    &:active {
      transform: none;
    }
  }
`;

const ComponentIcon = styled.div`
  font-size: 24px;
  color: ${props => props.color || theme.colors.primary.main};
  margin-bottom: ${theme.spacing[2]};
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: ${props => props.background || `rgba(37, 99, 235, 0.1)`};
  margin: 0 auto ${theme.spacing[2]};
  transition: ${theme.transitions.default};
  
  ${ComponentCard}:hover & {
    background: ${props => props.hoverBackground || `rgba(37, 99, 235, 0.2)`};
    transform: scale(1.1);
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    border: 1px solid currentColor;
  }
`;

const ComponentLabel = styled.div`
  font-size: ${theme.typography.fontSize.sm};
  font-weight: ${theme.typography.fontWeight.medium};
  color: ${theme.colors.text.primary};
  margin-bottom: ${theme.spacing[1]};
  line-height: ${theme.typography.lineHeight.tight};
`;

const ComponentDescription = styled.div`
  font-size: ${theme.typography.fontSize.xs};
  color: ${theme.colors.text.secondary};
  line-height: ${theme.typography.lineHeight.normal};
  text-align: center;
  margin-top: ${theme.spacing[1]};
`;

const CategoryHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${theme.spacing[3]} ${theme.spacing[4]};
  background: ${theme.colors.background.tertiary};
  border-bottom: 1px solid ${theme.colors.border.light};
  font-weight: ${theme.typography.fontWeight.semibold};
  color: ${theme.colors.text.primary};
  cursor: pointer;
  transition: ${theme.transitions.default};
  
  &:hover {
    background: ${theme.colors.interactive.hover};
  }
  
  &:focus {
    ${a11yUtils.focusRing()};
  }
  
  /* Ensure keyboard accessibility */
  &[tabindex] {
    outline: none;
  }
`;

const ComponentGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: ${theme.spacing[2]};
  padding: ${theme.spacing[4]};
  
  /* Responsive adjustments */
  ${theme.mediaQueries.maxMd} {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: ${theme.spacing[1]};
    padding: ${theme.spacing[2]};
  }
`;

const DragIndicator = styled.div`
  position: absolute;
  top: ${theme.spacing[1]};
  right: ${theme.spacing[1]};
  color: ${theme.colors.text.tertiary};
  font-size: ${theme.typography.fontSize.xs};
  opacity: 0;
  transition: ${theme.transitions.default};
  
  ${ComponentCard}:hover & {
    opacity: 1;
  }
`;

const ComponentPreview = styled.div`
  position: absolute;
  top: ${theme.spacing[1]};
  left: ${theme.spacing[1]};
  width: 20px;
  height: 20px;
  background: ${theme.colors.background.paper};
  border: 1px solid ${theme.colors.border.light};
  border-radius: ${theme.borderRadius.sm};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  opacity: 0;
  transition: ${theme.transitions.default};
  
  &.component-preview {
    opacity: 0.7;
  }
`;

const TagContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${theme.spacing[1]};
  margin-top: ${theme.spacing[1]};
  justify-content: center;
`;

const ComponentTag = styled.span`
  background: ${theme.colors.neutral[100]};
  color: ${theme.colors.text.secondary};
  padding: 2px ${theme.spacing[1]};
  border-radius: ${theme.borderRadius.sm};
  font-size: 10px;
  font-weight: ${theme.typography.fontWeight.medium};
`;

const PriorityBadge = styled.div`
  position: absolute;
  top: ${theme.spacing[1]};
  right: ${theme.spacing[1]};
  background: ${props => props.priority === 'high' ? theme.colors.success.main : theme.colors.primary.main};
  color: ${theme.colors.background.paper};
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  z-index: 2;
`;

const ScrollableContent = styled.div`
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  
  /* Custom scrollbar styling */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: ${theme.colors.background.secondary};
  }
  
  &::-webkit-scrollbar-thumb {
    background: ${theme.colors.border.medium};
    border-radius: ${theme.borderRadius.full};
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: ${theme.colors.border.dark};
  }
`;

// Component data with enhanced metadata
const COMPONENT_GROUPS = [
  {
    id: 'layout',
    title: 'Layout',
    description: 'Structural components for organizing content',
    color: theme.colors.success.main,
    icon: <LayoutOutlined />,
    components: [
      {
        type: 'header',
        icon: <FontSizeOutlined />,
        label: 'Header',
        description: 'Page or section header with title and navigation',
        usage: 'Use for page titles, navigation bars, or section headers',
        tags: ['layout', 'navigation', 'title'],
        complexity: 'simple',
        preview: '═══'
      },
      {
        type: 'section',
        icon: <LayoutOutlined />,
        label: 'Section',
        description: 'Container for grouping related content',
        usage: 'Organize content into logical sections',
        tags: ['layout', 'container', 'organization'],
        complexity: 'simple',
        preview: '▢'
      },
      {
        type: 'card',
        icon: <CreditCardOutlined />,
        label: 'Card',
        description: 'Flexible content container with optional header and footer',
        usage: 'Display content in a clean, contained format',
        tags: ['layout', 'container', 'content'],
        complexity: 'medium',
        preview: '▢'
      },
      {
        type: 'tabs',
        icon: <BarsOutlined />,
        label: 'Tabs',
        description: 'Tabbed interface for organizing content',
        usage: 'Switch between different views or content sections',
        tags: ['layout', 'navigation', 'organization'],
        complexity: 'medium',
        preview: '┬┬┬'
      },
      {
        type: 'divider',
        icon: <BarsOutlined />,
        label: 'Divider',
        description: 'Visual separator between content sections',
        usage: 'Separate content sections visually',
        tags: ['layout', 'separator', 'visual'],
        complexity: 'simple',
        preview: '───'
      }
    ]
  },
  {
    id: 'basic',
    title: 'Basic Components',
    description: 'Essential UI elements for content and interaction',
    color: theme.colors.primary.main,
    icon: <AppstoreOutlined />,
    components: [
      {
        type: 'text',
        icon: <FileTextOutlined />,
        label: 'Text',
        description: 'Formatted text content with typography options',
        usage: 'Display paragraphs, headings, and formatted text',
        tags: ['content', 'text', 'typography'],
        complexity: 'simple',
        preview: 'Aa'
      },
      {
        type: 'button',
        icon: <AppstoreOutlined />,
        label: 'Button',
        description: 'Interactive button for user actions',
        usage: 'Trigger actions, submit forms, or navigate',
        tags: ['interaction', 'action', 'click'],
        complexity: 'simple',
        preview: '▢'
      },
      {
        type: 'image',
        icon: <PictureOutlined />,
        label: 'Image',
        description: 'Display images with responsive sizing',
        usage: 'Show photos, illustrations, or graphics',
        tags: ['media', 'visual', 'content'],
        complexity: 'simple',
        preview: '🖼'
      },
      {
        type: 'list',
        icon: <OrderedListOutlined />,
        label: 'List',
        description: 'Ordered or unordered list of items',
        usage: 'Display collections of related items',
        tags: ['content', 'organization', 'items'],
        complexity: 'medium',
        preview: '• • •'
      },
      {
        type: 'tag',
        icon: <TagsOutlined />,
        label: 'Tag',
        description: 'Small label for categorization or status',
        usage: 'Label content, show status, or categorize',
        tags: ['label', 'status', 'category'],
        complexity: 'simple',
        preview: '🏷'
      }
    ]
  }
];

export default function UXEnhancedComponentPalette({
  onAddComponent,
  onDragStart,
  onDragEnd,
  components = [],
  selectedComponent = null,
  showAISuggestions = true,
  loading = false
}) {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedCategories, setExpandedCategories] = useState(['layout', 'basic']);
  const [showDescriptions, setShowDescriptions] = useState(true);
  const [showTags, setShowTags] = useState(false);
  const [filterBy, setFilterBy] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [draggedComponent, setDraggedComponent] = useState(null);
  const dragPreviewRef = useRef(null);

  // AI suggestions hook
  const {
    suggestions,
    loading: aiLoading,
    applyComponentCombination,
    hasLayoutSuggestions,
    hasCombinationSuggestions
  } = useAIDesignSuggestions({
    autoRefresh: true,
    context: { selectedComponent }
  });

  // Filter and sort components
  const filteredGroups = useMemo(() => {
    let groups = [...COMPONENT_GROUPS];

    // Add AI suggestions group if enabled
    if (showAISuggestions && hasCombinationSuggestions) {
      groups.unshift({
        id: 'ai',
        title: 'AI Suggestions',
        description: 'Smart component recommendations based on your current app',
        color: theme.colors.accent.main,
        icon: <RobotOutlined />,
        isAI: true,
        components: [] // Will be populated with AI suggestions
      });
    }

    // Filter by search term
    if (searchTerm) {
      groups = groups.map(group => ({
        ...group,
        components: group.components.filter(component =>
          component.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
          component.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          component.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
        )
      })).filter(group => group.components.length > 0 || group.isAI);
    }

    // Filter by complexity
    if (filterBy !== 'all') {
      groups = groups.map(group => ({
        ...group,
        components: group.components.filter(component => component.complexity === filterBy)
      })).filter(group => group.components.length > 0 || group.isAI);
    }

    // Sort components within each group
    groups = groups.map(group => ({
      ...group,
      components: [...group.components].sort((a, b) => {
        switch (sortBy) {
          case 'name':
            return a.label.localeCompare(b.label);
          case 'complexity':
            const complexityOrder = { simple: 0, medium: 1, complex: 2 };
            return complexityOrder[a.complexity] - complexityOrder[b.complexity];
          default:
            return 0;
        }
      })
    }));

    return groups;
  }, [searchTerm, filterBy, sortBy, showAISuggestions, hasCombinationSuggestions]);

  const handleDragStart = useCallback((e, component) => {
    setDraggedComponent(component);

    // Set drag data
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: component.type,
      label: component.label,
      source: 'palette'
    }));
    e.dataTransfer.effectAllowed = 'copy';

    // Create custom drag image
    if (dragPreviewRef.current) {
      const dragImage = dragPreviewRef.current.cloneNode(true);
      dragImage.style.transform = 'rotate(2deg)';
      dragImage.style.opacity = '0.8';
      e.dataTransfer.setDragImage(dragImage, 70, 35);
    }

    if (onDragStart) {
      onDragStart(component);
    }
  }, [onDragStart]);

  const handleDragEnd = useCallback((e) => {
    setDraggedComponent(null);
    if (onDragEnd) {
      onDragEnd();
    }
  }, [onDragEnd]);

  const handleCategoryToggle = useCallback((categoryId) => {
    setExpandedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(cat => cat !== categoryId)
        : [...prev, categoryId]
    );
  }, []);

  const handleKeyDown = useCallback((e, action) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      action();
    }
  }, []);

  // Filter menu for dropdown
  const filterMenu = (
    <Menu
      selectedKeys={[filterBy]}
      onClick={({ key }) => setFilterBy(key)}
    >
      <Menu.Item key="all">All Components</Menu.Item>
      <Menu.Item key="simple">Simple</Menu.Item>
      <Menu.Item key="medium">Medium</Menu.Item>
      <Menu.Item key="complex">Complex</Menu.Item>
    </Menu>
  );

  // Sort menu for dropdown
  const sortMenu = (
    <Menu
      selectedKeys={[sortBy]}
      onClick={({ key }) => setSortBy(key)}
    >
      <Menu.Item key="name">Sort by Name</Menu.Item>
      <Menu.Item key="complexity">Sort by Complexity</Menu.Item>
    </Menu>
  );

  return (
    <PaletteContainer role="region" aria-label="Component Palette">
      <PaletteHeader>
        <Title level={5} style={{ margin: 0, color: 'white' }}>
          Component Palette
        </Title>
        <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
          Drag components to the canvas or click to add
        </Text>
      </PaletteHeader>

      <SearchContainer>
        <Search
          placeholder="Search components..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          prefix={<SearchOutlined />}
          allowClear
          style={{ marginBottom: theme.spacing[2] }}
          aria-label="Search components"
        />
        
        <FilterControls>
          <Space size="small">
            <Dropdown overlay={filterMenu} trigger={['click']}>
              <Button size="small" icon={<FilterOutlined />}>
                Filter <DownOutlined />
              </Button>
            </Dropdown>
            
            <Dropdown overlay={sortMenu} trigger={['click']}>
              <Button size="small">
                Sort <DownOutlined />
              </Button>
            </Dropdown>
          </Space>
          
          <Space size="small">
            <Text style={{ fontSize: theme.typography.fontSize.xs }}>Descriptions:</Text>
            <Switch
              size="small"
              checked={showDescriptions}
              onChange={setShowDescriptions}
              aria-label="Toggle component descriptions"
            />
            
            <Text style={{ fontSize: theme.typography.fontSize.xs }}>Tags:</Text>
            <Switch
              size="small"
              checked={showTags}
              onChange={setShowTags}
              aria-label="Toggle component tags"
            />
          </Space>
        </FilterControls>
      </SearchContainer>

      <ScrollableContent>
        {loading && (
          <div style={{ padding: theme.spacing[8], textAlign: 'center' }}>
            <Spin size="large" tip="Loading components..." />
          </div>
        )}

        {!loading && filteredGroups.map((group) => (
          <div key={group.id}>
            <CategoryHeader
              onClick={() => handleCategoryToggle(group.id)}
              onKeyDown={(e) => handleKeyDown(e, () => handleCategoryToggle(group.id))}
              tabIndex={0}
              role="button"
              aria-expanded={expandedCategories.includes(group.id)}
              aria-controls={`category-${group.id}`}
            >
              <Space>
                <div
                  style={{
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    backgroundColor: group.color
                  }}
                />
                {group.icon}
                <span>{group.title}</span>
                <Badge count={group.components.length} size="small" />
                {group.isAI && aiLoading && (
                  <Badge status="processing" />
                )}
              </Space>
              <DownOutlined
                style={{
                  transform: expandedCategories.includes(group.id) ? 'rotate(180deg)' : 'rotate(0deg)',
                  transition: theme.transitions.default
                }}
              />
            </CategoryHeader>

            {expandedCategories.includes(group.id) && (
              <div id={`category-${group.id}`}>
                {/* AI-powered combination suggestions */}
                {group.isAI && hasCombinationSuggestions && (
                  <div style={{ padding: theme.spacing[4], borderBottom: `1px solid ${theme.colors.border.light}` }}>
                    <Text type="secondary" style={{ fontSize: theme.typography.fontSize.xs, display: 'block', marginBottom: theme.spacing[2] }}>
                      <BulbOutlined /> AI-powered component combinations:
                    </Text>
                    <ComponentCombinationsList
                      suggestions={suggestions.combinations}
                      onApply={applyComponentCombination}
                      loading={aiLoading}
                      compact={true}
                      showScore={false}
                      showPreview={false}
                      emptyMessage="No AI combinations available"
                    />
                  </div>
                )}

                <ComponentGrid role="grid" aria-label={`${group.title} components`}>
                  {group.components.map((component) => (
                    <ComponentCard
                      key={component.type}
                      size="small"
                      hoverable
                      isDragging={draggedComponent?.type === component.type}
                      draggable
                      onDragStart={(e) => handleDragStart(e, component)}
                      onDragEnd={handleDragEnd}
                      onClick={() => onAddComponent(component.type)}
                      onKeyDown={(e) => handleKeyDown(e, () => onAddComponent(component.type))}
                      ref={draggedComponent?.type === component.type ? dragPreviewRef : null}
                      tabIndex={0}
                      role="gridcell"
                      aria-label={`Add ${component.label} component`}
                    >
                      <DragIndicator aria-hidden="true">
                        <DragOutlined />
                      </DragIndicator>

                      <ComponentPreview className="component-preview" aria-hidden="true">
                        {component.preview}
                      </ComponentPreview>

                      <ComponentIcon
                        color={group.color}
                        background={`${group.color}15`}
                        hoverBackground={`${group.color}25`}
                      >
                        {component.icon}
                      </ComponentIcon>

                      <ComponentLabel>{component.label}</ComponentLabel>

                      {showDescriptions && (
                        <ComponentDescription>
                          {component.description}
                        </ComponentDescription>
                      )}

                      {showTags && (
                        <TagContainer>
                          {component.tags.slice(0, 3).map(tag => (
                            <ComponentTag key={tag}>{tag}</ComponentTag>
                          ))}
                        </TagContainer>
                      )}

                      <Tooltip
                        title={
                          <div>
                            <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
                              {component.label}
                            </div>
                            <div style={{ marginBottom: 8 }}>
                              {component.description}
                            </div>
                            <div style={{ fontSize: 11, color: '#ccc' }}>
                              <strong>Usage:</strong> {component.usage}
                            </div>
                            <div style={{ fontSize: 11, color: '#ccc', marginTop: 4 }}>
                              <strong>Tags:</strong> {component.tags.join(', ')}
                            </div>
                            <div style={{ fontSize: 11, color: '#ccc', marginTop: 4 }}>
                              <strong>Complexity:</strong> {component.complexity}
                            </div>
                          </div>
                        }
                        placement="right"
                      >
                        <InfoCircleOutlined
                          style={{
                            position: 'absolute',
                            bottom: theme.spacing[1],
                            left: theme.spacing[1],
                            fontSize: 10,
                            color: theme.colors.text.tertiary,
                            opacity: 0.7
                          }}
                          aria-hidden="true"
                        />
                      </Tooltip>
                    </ComponentCard>
                  ))}
                </ComponentGrid>
              </div>
            )}
          </div>
        ))}

        {!loading && filteredGroups.length === 0 && (
          <div style={{ padding: theme.spacing[8], textAlign: 'center' }}>
            <Empty
              image={<SearchOutlined style={{ fontSize: 48, color: theme.colors.text.tertiary }} />}
              description={
                <div>
                  <div style={{ marginBottom: theme.spacing[2] }}>
                    No components found matching "{searchTerm}"
                  </div>
                  <Button
                    type="link"
                    size="small"
                    onClick={() => setSearchTerm('')}
                    style={{ padding: 0 }}
                  >
                    Clear search
                  </Button>
                </div>
              }
            />
          </div>
        )}
      </ScrollableContent>
    </PaletteContainer>
  );
}
