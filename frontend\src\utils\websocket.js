/**
 * WebSocket utility functions for the App Builder application.
 */

/**
 * Get the WebSocket URL based on the current environment and endpoint.
 *
 * @param {string} endpoint - The WebSocket endpoint (e.g., 'app_builder', 'test')
 * @param {Object} options - Additional options
 * @param {boolean} options.forceSecure - Force secure WebSocket connection (wss://)
 * @returns {string} The WebSocket URL
 */
export function getWebSocketUrl(endpoint = '', options = {}) {
  const { forceSecure = false } = options;

  // Get the base URL from environment variables or use the current host
  const websocketUrl = process.env.WEBSOCKET_URL || '';

  if (websocketUrl) {
    // Use the configured WebSocket URL if available
    return `${websocketUrl}/${endpoint}`.replace(/\/+$/, '');
  }

  // Determine protocol (ws:// or wss://)
  const isSecure = forceSecure || window.location.protocol === 'https:';
  const protocol = isSecure ? 'wss://' : 'ws://';

  // Use the current host and backend port
  const host = window.location.hostname;

  // Determine the port based on the environment
  let port;
  if (process.env.REACT_APP_BACKEND_PORT) {
    port = process.env.REACT_APP_BACKEND_PORT;
  } else if (window.location.port) {
    // If we're on a non-standard port, use 8000 for backend
    port = '8000';
  } else {
    // For standard ports (80/443), use the same port
    port = window.location.port || (isSecure ? '443' : '80');
  }

  // Construct the WebSocket URL
  let url;

  // For localhost development, connect directly to backend (bypass proxy issues)
  if (host === 'localhost' || host === '127.0.0.1') {
    // Connect directly to backend port 8000 to bypass proxy issues
    url = `${protocol}${host}:8000/ws`;

    // Fallback options if the main URL doesn't work
    window.WEBSOCKET_FALLBACK_URLS = [
      `${protocol}${host}:8000/ws`,
      `/ws`,
      `${protocol}${host}:${port}/ws`,
      `${protocol}127.0.0.1:8000/ws`
    ];
  } else if (host === 'backend') {
    // Special case for Docker container-to-container communication
    url = `${protocol}${host}:8000/ws`;
  } else {
    // For production, use the configured port
    url = `${protocol}${host}:${port}/ws`;

    // Fallback options if the main URL doesn't work
    window.WEBSOCKET_FALLBACK_URLS = [
      `${protocol}${host}:${port}/ws`,
      `${protocol}${host}:8000/ws`
    ];
  }

  // Log the WebSocket URL for debugging
  console.log(`Constructing WebSocket URL: ${url}`);

  // Add the endpoint if provided
  if (endpoint) {
    url += `/${endpoint}`;
  }

  // Ensure the URL ends with a trailing slash for Django Channels
  if (!url.endsWith('/')) {
    url += '/';
  }

  return url;
}

/**
 * Create a WebSocket connection with automatic reconnection.
 *
 * @param {Object} options - WebSocket options
 * @param {string} options.url - The WebSocket URL
 * @param {boolean} options.autoReconnect - Whether to automatically reconnect
 * @param {number} options.reconnectInterval - Reconnection interval in milliseconds
 * @param {number} options.maxReconnectAttempts - Maximum number of reconnection attempts
 * @param {Function} options.onOpen - Callback when the connection is opened
 * @param {Function} options.onMessage - Callback when a message is received
 * @param {Function} options.onClose - Callback when the connection is closed
 * @param {Function} options.onError - Callback when an error occurs
 * @returns {WebSocket} The WebSocket instance
 */
export function createWebSocket(options) {
  const {
    url,
    autoReconnect = true,
    reconnectInterval = 1000,
    maxReconnectAttempts = 5,
    onOpen,
    onMessage,
    onClose,
    onError
  } = options;

  let reconnectAttempts = 0;
  let socket = null;

  // Create the WebSocket connection
  function connect() {
    try {
      // Close existing connection if any
      if (socket) {
        socket.close();
      }

      // Log WebSocket class information for debugging
      console.log('🔌 Creating WebSocket connection...');
      console.log('🔌 WebSocket class:', window.WebSocket?.name || 'WebSocket');
      console.log('🔌 Is mock WebSocket?', window.WebSocket?.name === 'MockWebSocket');
      console.log('🔌 URL:', url);

      // Create new WebSocket connection
      socket = new WebSocket(url);

      // Log the created socket for debugging
      console.log('🔌 WebSocket instance created:', socket.constructor.name);

      // Connection opened
      socket.onopen = (event) => {
        console.log(`WebSocket connected to ${url}`);
        reconnectAttempts = 0;

        // Call the onOpen callback if provided
        if (typeof onOpen === 'function') {
          onOpen(event);
        }
      };

      // Listen for messages
      socket.onmessage = (event) => {
        // Call the onMessage callback if provided
        if (typeof onMessage === 'function') {
          onMessage(event);
        }
      };

      // Connection closed
      socket.onclose = (event) => {
        console.log(`WebSocket connection closed: ${event.code} ${event.reason}`);

        // Call the onClose callback if provided
        if (typeof onClose === 'function') {
          onClose(event);
        }

        // Reconnect if enabled and not a normal closure
        if (autoReconnect && event.code !== 1000 && event.code !== 1001) {
          reconnect();
        }
      };

      // Connection error
      socket.onerror = (event) => {
        console.error('WebSocket error:', event);

        // Call the onError callback if provided
        if (typeof onError === 'function') {
          onError(event);
        }
      };

      return socket;
    } catch (error) {
      console.error('Error creating WebSocket:', error);

      // Call the onError callback if provided
      if (typeof onError === 'function') {
        onError(error);
      }

      // Reconnect if enabled
      if (autoReconnect) {
        reconnect();
      }

      return null;
    }
  }

  // Reconnect to the WebSocket server
  function reconnect() {
    if (reconnectAttempts >= maxReconnectAttempts) {
      console.log(`Maximum reconnection attempts (${maxReconnectAttempts}) reached`);
      return;
    }

    reconnectAttempts++;

    const delay = reconnectInterval * Math.pow(1.5, reconnectAttempts - 1);
    console.log(`Reconnecting to WebSocket in ${delay}ms (attempt ${reconnectAttempts}/${maxReconnectAttempts})`);

    setTimeout(connect, delay);
  }

  // Initial connection
  connect();

  // Return the WebSocket instance and control functions
  return {
    socket,
    reconnect,
    close: () => {
      if (socket) {
        socket.close(1000, 'User initiated disconnect');
      }
    },
    send: (data) => {
      if (socket && socket.readyState === WebSocket.OPEN) {
        const message = typeof data === 'string' ? data : JSON.stringify(data);
        socket.send(message);
      } else {
        console.error('Cannot send message: WebSocket is not connected');
      }
    }
  };
}

/**
 * Check if WebSockets are supported in the current browser.
 *
 * @returns {boolean} Whether WebSockets are supported
 */
export function isWebSocketSupported() {
  return 'WebSocket' in window;
}

/**
 * Handle WebSocket errors and provide helpful messages.
 *
 * @param {Error} error - The WebSocket error
 * @returns {string} A user-friendly error message
 */
export function getWebSocketErrorMessage(error) {
  if (!isWebSocketSupported()) {
    return 'WebSockets are not supported in your browser. Please upgrade to a modern browser.';
  }

  if (error instanceof Event) {
    return 'A WebSocket error occurred. Please check your connection and try again.';
  }

  if (error instanceof DOMException && error.name === 'SecurityError') {
    return 'WebSocket connection blocked due to security restrictions. Please check your browser settings.';
  }

  return error.message || 'An unknown WebSocket error occurred.';
}
