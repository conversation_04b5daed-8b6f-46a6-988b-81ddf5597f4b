import React, { useState } from 'react';
import { <PERSON>po<PERSON>, <PERSON><PERSON>, <PERSON>ton, Select, Space, Tooltip } from 'antd';
import { PlayCircleOutlined, SaveOutlined, CopyOutlined, UndoOutlined, RedoOutlined } from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

const CodePanel = () => {
  const [activeTab, setActiveTab] = useState('1');

  // Mock code examples
  const jsCode = `// Custom JavaScript function
function calculateTotal(items) {
  return items.reduce((total, item) => {
    return total + (item.price * item.quantity);
  }, 0);
}

// Example event handler with CSRF protection
async function handleSubmit(event) {
  event.preventDefault();
  const formData = new FormData(event.target);
  const data = Object.fromEntries(formData);

  try {
    // Get CSRF token
    const csrfResponse = await fetch('/api/csrf-token/', {
      method: 'GET',
      credentials: 'include'
    });
    const csrfData = await csrfResponse.json();

    // Send data to API with CSRF token
    const response = await fetch('/api/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': csrfData.csrfToken
      },
      credentials: 'include', // Include cookies
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(\`Request failed with status \${response.status}\`);
    }

    const result = await response.json();
    console.log('Success:', result);
    showNotification('Form submitted successfully');
  } catch (error) {
    console.error('Error:', error);
    showNotification('Error submitting form', 'error');
  }
}

// Alternative: Using the CSRF service (recommended)
import csrfService from '../services/csrfService';

async function handleSubmitWithService(event) {
  event.preventDefault();
  const formData = new FormData(event.target);
  const data = Object.fromEntries(formData);

  try {
    // Use the CSRF service for automatic token handling
    const response = await csrfService.request('/api/submit', {
      method: 'POST',
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(\`Request failed with status \${response.status}\`);
    }

    const result = await response.json();
    console.log('Success:', result);
    showNotification('Form submitted successfully');
  } catch (error) {
    console.error('Error:', error);
    showNotification('Error submitting form', 'error');
  }
}`;

  const cssCode = `/* Custom CSS styles */
.custom-header {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.custom-card {
  transition: all 0.3s ease;
}

.custom-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.highlight-text {
  color: #1890ff;
  font-weight: bold;
}

/* Responsive styles */
@media (max-width: 768px) {
  .custom-header {
    padding: 10px;
  }
  
  .mobile-hidden {
    display: none;
  }
}`;

  const htmlCode = `<!-- Custom HTML template -->
<div class="custom-container">
  <header class="custom-header">
    <h1>{{title}}</h1>
    <p>{{subtitle}}</p>
  </header>
  
  <main>
    <div class="card-container">
      {{#each items}}
        <div class="custom-card">
          <h3>{{this.title}}</h3>
          <p>{{this.description}}</p>
          <button onclick="handleItemClick({{this.id}})">
            View Details
          </button>
        </div>
      {{/each}}
    </div>
  </main>
  
  <footer>
    <p>&copy; {{currentYear}} - {{appName}}</p>
  </footer>
</div>`;

  return (
    <div>
      <Title level={5}>Custom Code</Title>
      <Paragraph>
        Add custom code to extend your application's functionality.
      </Paragraph>

      <div style={{ marginBottom: '16px' }}>
        <Space>
          <Select defaultValue="global" style={{ width: 150 }}>
            <Option value="global">Global Scope</Option>
            <Option value="component">Selected Component</Option>
            <Option value="page">Current Page</Option>
          </Select>

          <Tooltip title="Run Code">
            <Button icon={<PlayCircleOutlined />} />
          </Tooltip>
          <Tooltip title="Save">
            <Button icon={<SaveOutlined />} />
          </Tooltip>
          <Tooltip title="Copy">
            <Button icon={<CopyOutlined />} />
          </Tooltip>
          <Tooltip title="Undo">
            <Button icon={<UndoOutlined />} />
          </Tooltip>
          <Tooltip title="Redo">
            <Button icon={<RedoOutlined />} />
          </Tooltip>
        </Space>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="JavaScript" key="1">
          <div style={{
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            padding: '8px',
            fontFamily: 'monospace',
            fontSize: '14px',
            lineHeight: '1.5',
            backgroundColor: '#f5f5f5',
            height: '300px',
            overflow: 'auto',
            whiteSpace: 'pre',
            color: '#333'
          }}>
            {jsCode}
          </div>
        </TabPane>
        <TabPane tab="CSS" key="2">
          <div style={{
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            padding: '8px',
            fontFamily: 'monospace',
            fontSize: '14px',
            lineHeight: '1.5',
            backgroundColor: '#f5f5f5',
            height: '300px',
            overflow: 'auto',
            whiteSpace: 'pre',
            color: '#333'
          }}>
            {cssCode}
          </div>
        </TabPane>
        <TabPane tab="HTML" key="3">
          <div style={{
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            padding: '8px',
            fontFamily: 'monospace',
            fontSize: '14px',
            lineHeight: '1.5',
            backgroundColor: '#f5f5f5',
            height: '300px',
            overflow: 'auto',
            whiteSpace: 'pre',
            color: '#333'
          }}>
            {htmlCode}
          </div>
        </TabPane>
      </Tabs>

      <div style={{ marginTop: '16px' }}>
        <Title level={5}>Code Snippets</Title>
        <Select
          style={{ width: '100%' }}
          placeholder="Insert a code snippet"
          options={[
            { value: 'fetch', label: 'Fetch API Request' },
            { value: 'form', label: 'Form Submission Handler' },
            { value: 'modal', label: 'Show Modal Dialog' },
            { value: 'notification', label: 'Display Notification' },
            { value: 'localStorage', label: 'Local Storage Operations' },
            { value: 'validation', label: 'Form Validation' },
            { value: 'animation', label: 'CSS Animation' },
            { value: 'responsive', label: 'Responsive Media Queries' },
          ]}
        />
      </div>
    </div>
  );
};

export default CodePanel;
