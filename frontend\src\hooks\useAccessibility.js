import { useState, useEffect } from 'react';

const useAccessibility = () => {
  // Initialize state from localStorage if available
  const [textSize, setTextSize] = useState(() => {
    const saved = localStorage.getItem('accessibility_textSize');
    return saved ? parseInt(saved, 10) : 100;
  });

  const [highContrast, setHighContrast] = useState(() => {
    const saved = localStorage.getItem('accessibility_highContrast');
    return saved === 'true';
  });

  const [enhancedContrast, setEnhancedContrast] = useState(() => {
    const saved = localStorage.getItem('accessibility_enhancedContrast');
    return saved === 'true';
  });

  const [reducedMotion, setReducedMotion] = useState(() => {
    const saved = localStorage.getItem('accessibility_reducedMotion');
    return saved === 'true';
  });

  const [colorBlindMode, setColorBlindMode] = useState(() => {
    const saved = localStorage.getItem('accessibility_colorBlindMode');
    return saved || 'none';
  });

  // Update localStorage when settings change
  useEffect(() => {
    localStorage.setItem('accessibility_textSize', textSize.toString());
    document.documentElement.style.fontSize = `${textSize}%`;
  }, [textSize]);

  useEffect(() => {
    localStorage.setItem('accessibility_highContrast', highContrast.toString());
    if (highContrast) {
      document.body.classList.add('high-contrast');
    } else {
      document.body.classList.remove('high-contrast');
    }
  }, [highContrast]);

  useEffect(() => {
    localStorage.setItem('accessibility_enhancedContrast', enhancedContrast.toString());
    if (enhancedContrast) {
      document.body.classList.add('enhanced-contrast');
    } else {
      document.body.classList.remove('enhanced-contrast');
    }
  }, [enhancedContrast]);

  useEffect(() => {
    localStorage.setItem('accessibility_reducedMotion', reducedMotion.toString());
    if (reducedMotion) {
      document.body.classList.add('reduced-motion');
    } else {
      document.body.classList.remove('reduced-motion');
    }
  }, [reducedMotion]);

  useEffect(() => {
    localStorage.setItem('accessibility_colorBlindMode', colorBlindMode);
    // Remove any existing color blind mode classes
    document.body.classList.remove('protanopia', 'deuteranopia', 'tritanopia');

    // Add the selected color blind mode class if not 'none'
    if (colorBlindMode !== 'none') {
      document.body.classList.add(colorBlindMode);
    }
  }, [colorBlindMode]);

  return {
    textSize,
    setTextSize,
    highContrast,
    setHighContrast,
    enhancedContrast,
    setEnhancedContrast,
    reducedMotion,
    setReducedMotion,
    colorBlindMode,
    setColorBlindMode
  };
};

export default useAccessibility;
