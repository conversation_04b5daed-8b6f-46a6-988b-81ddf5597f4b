# App Builder API Documentation

This document provides comprehensive documentation for the App Builder API, including REST API endpoints, WebSocket endpoints, authentication, and real-time collaboration features.

## Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
   - [CSRF Token Authentication](#csrf-token-authentication)
   - [JWT Authentication](#jwt-authentication)
   - [Token Authentication](#token-authentication)
3. [HTTP REST API](#http-rest-api)
   - [Base URLs and Versioning](#base-urls-and-versioning)
   - [Request/Response Format](#requestresponse-format)
   - [Error Handling](#error-handling)
   - [Rate Limiting](#rate-limiting)
   - [App Management](#app-management)
   - [Template Management](#template-management)
   - [User Management](#user-management)
   - [AI Features](#ai-features)
   - [Health and Monitoring](#health-and-monitoring)
4. [WebSocket API](#websocket-api)
   - [Connection](#connection)
   - [Message Format](#message-format)
   - [App Builder WebSocket](#app-builder-websocket)
   - [Collaboration WebSocket](#collaboration-websocket)
   - [AI Suggestions WebSocket](#ai-suggestions-websocket)
   - [Notifications WebSocket](#notifications-websocket)
   - [Performance Monitoring WebSocket](#performance-monitoring-websocket)
5. [GraphQL API](#graphql-api)
6. [Security](#security)
7. [Development vs Production](#development-vs-production)
8. [Integration Examples](#integration-examples)

## Overview

The App Builder API provides a comprehensive set of endpoints for building, managing, and collaborating on web applications. The API supports both REST and real-time WebSocket communication, with features including:

- **App Management**: Create, read, update, and delete applications
- **Template System**: Reusable component, layout, and app templates
- **Real-time Collaboration**: Multi-user editing with live updates
- **AI-Powered Features**: Layout suggestions and component recommendations
- **Version Control**: App versioning and history management
- **Authentication**: Multiple authentication methods (CSRF, JWT, Token)
- **Performance Monitoring**: Real-time performance metrics

## Authentication

The App Builder API supports multiple authentication methods to accommodate different use cases and security requirements.

### CSRF Token Authentication

For web applications using session-based authentication, CSRF tokens are required for state-changing operations.

#### Get CSRF Token

```http
GET /api/csrf-token/
```

**Response:**
```json
{
  "csrfToken": "abc123def456..."
}
```

**Usage:**
Include the CSRF token in requests using one of these methods:
- Header: `X-CSRFToken: <token>`
- Form field: `csrfmiddlewaretoken=<token>`
- Cookie: `csrftoken=<token>` (automatically handled by browsers)

### JWT Authentication

JSON Web Tokens provide stateless authentication suitable for mobile apps and SPAs.

#### Login with JWT

```http
POST /api/auth/login/
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

**Response:**
```json
{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": 1,
    "username": "your_username",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe"
  },
  "expires_at": "2024-01-01T12:00:00Z"
}
```

#### Register User

```http
POST /api/auth/register/
Content-Type: application/json

{
  "username": "new_user",
  "email": "<EMAIL>",
  "password": "secure_password",
  "first_name": "John",
  "last_name": "Doe"
}
```

**Rate Limit:** 3 requests per hour per IP

#### Using JWT Tokens

Include the JWT token in the Authorization header:
```http
Authorization: Bearer <jwt_token>
```

### Token Authentication

Traditional DRF token authentication for API access.

#### Get Auth Token

```http
POST /api/token-auth/
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

**Response:**
```json
{
  "token": "abc123def456...",
  "user_id": 1,
  "email": "<EMAIL>"
}
```

**Usage:**
```http
Authorization: Token <token>
```

## HTTP REST API

### Base URLs and Versioning

The API supports versioning through URL paths:

- **Base URL:** `/api/`
- **Version 1:** `/api/v1/` (current stable)
- **Version 2:** `/api/v2/` (future use)
- **Unversioned:** `/api/` (defaults to v1 for backward compatibility)

### Request/Response Format

#### Content Types
- **Request:** `application/json`
- **Response:** `application/json`

#### Standard Response Structure

**Success Response:**
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully"
}
```

**Error Response:**
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": { ... }
  },
  "status": 400
}
```

#### Pagination

List endpoints support pagination with the following parameters:
- `page`: Page number (default: 1)
- `page_size`: Items per page (default: 20, max: 100)

**Paginated Response:**
```json
{
  "count": 150,
  "next": "http://api.example.com/api/v1/apps/?page=3",
  "previous": "http://api.example.com/api/v1/apps/?page=1",
  "results": [ ... ]
}
```

### Error Handling

#### HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | OK - Request successful |
| 201 | Created - Resource created successfully |
| 400 | Bad Request - Invalid request data |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server error |

#### Error Response Examples

**Validation Error:**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "name": ["This field is required."],
      "email": ["Enter a valid email address."]
    }
  },
  "status": 400
}
```

**Authentication Error:**
```json
{
  "success": false,
  "error": {
    "code": "AUTHENTICATION_REQUIRED",
    "message": "Authentication credentials were not provided",
    "details": {}
  },
  "status": 401
}
```

### Rate Limiting

Rate limits are applied per IP address and authenticated user:

| Endpoint Category | Limit | Window |
|------------------|-------|---------|
| Authentication | 5 requests | 1 minute |
| User Registration | 3 requests | 1 hour |
| General API | 100 requests | 1 minute |
| AI Features | 10 requests | 1 minute |

**Rate Limit Headers:**
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

### App Management

#### List Apps

```http
GET /api/v1/apps/
```

**Query Parameters:**
- `search`: Search by name or description
- `is_public`: Filter by public/private apps
- `user`: Filter by user ID
- `ordering`: Sort by `name`, `created_at`, `-created_at`

**Response:**
```json
{
  "count": 25,
  "next": "http://api.example.com/api/v1/apps/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "My App",
      "description": "A sample application",
      "user": 1,
      "app_data": "{\"components\": [], \"layouts\": []}",
      "app_data_json": {
        "components": [],
        "layouts": []
      },
      "created_at": "2024-01-01T12:00:00Z",
      "updated_at": "2024-01-01T12:30:00Z",
      "is_public": false
    }
  ]
}
```

#### Create App

```http
POST /api/v1/apps/
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "New App",
  "description": "My new application",
  "app_data": {
    "components": [],
    "layouts": [],
    "styles": {},
    "data": {}
  },
  "is_public": false
}
```

#### Get App Details

```http
GET /api/v1/apps/{id}/
```

#### Update App

```http
PUT /api/v1/apps/{id}/
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Updated App Name",
  "description": "Updated description",
  "app_data": {
    "components": [...],
    "layouts": [...],
    "styles": {...},
    "data": {...}
  }
}
```

#### Delete App

```http
DELETE /api/v1/apps/{id}/
Authorization: Bearer <token>
```

#### App Versioning

**Create Version:**
```http
POST /api/v1/apps/{id}/create_version/
Content-Type: application/json

{
  "version_name": "v1.1.0",
  "description": "Added new features"
}
```

**List Versions:**
```http
GET /api/v1/apps/{id}/versions/
```

**Restore Version:**
```http
POST /api/v1/apps/{id}/restore_version/
Content-Type: application/json

{
  "version_id": 5
}
```

### Template Management

#### Component Templates

**List Component Templates:**
```http
GET /api/v1/component-templates/
```

**Query Parameters:**
- `search`: Search by name or description
- `component_type`: Filter by component type
- `is_public`: Filter public templates

**Create Component Template:**
```http
POST /api/v1/component-templates/
Content-Type: application/json

{
  "name": "Custom Button",
  "description": "A reusable button component",
  "component_type": "Button",
  "default_props": {
    "text": "Click Me",
    "variant": "primary",
    "size": "medium"
  },
  "is_public": false
}
```

#### Layout Templates

**List Layout Templates:**
```http
GET /api/v1/layout-templates/
```

**Create Layout Template:**
```http
POST /api/v1/layout-templates/
Content-Type: application/json

{
  "name": "Three Column Grid",
  "description": "A responsive three-column layout",
  "layout_type": "grid",
  "components": {
    "slots": ["header", "main", "sidebar"],
    "configuration": {
      "columns": 3,
      "gap": "1rem"
    }
  },
  "default_props": {
    "responsive": true,
    "minColumnWidth": "200px"
  }
}
```

#### App Templates

**List App Templates:**
```http
GET /api/v1/app-templates/
```

**Query Parameters:**
- `app_category`: Filter by category (business, portfolio, blog, etc.)
- `search`: Search by name or description

**Create App Template:**
```http
POST /api/v1/app-templates/
Content-Type: application/json

{
  "name": "Business Landing Page",
  "description": "Complete landing page template",
  "app_category": "business",
  "components": {
    "header": {...},
    "hero": {...},
    "features": {...},
    "footer": {...}
  },
  "required_components": ["Header", "Hero", "FeatureGrid", "Footer"],
  "preview_image": "https://example.com/preview.jpg"
}
```

#### Featured Templates

```http
GET /api/featured-templates/
```

**Response:**
```json
{
  "components": [...],
  "layouts": [...],
  "apps": [...]
}
```

### User Management

#### Get User Profile

```http
GET /api/auth/profile/
Authorization: Bearer <token>
```

**Response:**
```json
{
  "id": 1,
  "username": "john_doe",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "date_joined": "2024-01-01T12:00:00Z",
  "is_active": true
}
```

#### Update User Profile

```http
PUT /api/auth/profile/update/
Content-Type: application/json
Authorization: Bearer <token>

{
  "first_name": "John",
  "last_name": "Smith",
  "email": "<EMAIL>"
}
```

### AI Features

#### Generate AI Suggestions

```http
POST /api/generate_ai_suggestions/
Content-Type: application/json
Authorization: Bearer <token>

{
  "components": [...],
  "layouts": [...],
  "context": {
    "app_type": "business",
    "target_audience": "professionals"
  }
}
```

**Rate Limit:** 10 requests per minute

#### Generate Image

```http
POST /api/generate_image/
Content-Type: application/json
Authorization: Bearer <token>

{
  "prompt": "A modern business logo",
  "style": "professional",
  "size": "512x512"
}
```

### Health and Monitoring

#### Health Check

```http
GET /api/health/
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "1.0.0",
  "database": "connected",
  "redis": "connected"
}
```

#### API Status

```http
GET /api/status/
```

#### Error Reporting

```http
POST /api/errors/
Content-Type: application/json

{
  "error_type": "javascript_error",
  "message": "TypeError: Cannot read property 'x' of undefined",
  "stack_trace": "...",
  "user_agent": "Mozilla/5.0...",
  "url": "/app-builder"
}
```

## WebSocket API

The WebSocket API provides real-time communication for collaborative editing, AI suggestions, notifications, and performance monitoring.

### Connection

WebSocket connections are established using the following endpoints:

#### App Builder WebSocket
```
ws://<host>/ws/app_builder/
wss://<host>/ws/app_builder/  (secure)
```

#### App-Specific WebSocket
```
ws://<host>/ws/app/{app_id}/
```

#### Collaboration WebSocket
```
ws://<host>/ws/collaboration/{session_id}/
```

#### AI Suggestions WebSocket
```
ws://<host>/ws/ai-suggestions/
```

#### Notifications WebSocket
```
ws://<host>/ws/notifications/
```

#### Performance Monitoring WebSocket
```
ws://<host>/ws/performance/
```

### Message Format

All WebSocket messages use JSON format:

```json
{
  "type": "message_type",
  "data": {
    // Message-specific data
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### App Builder WebSocket

#### Connection Authentication

Include authentication in the connection:
- **Query Parameter:** `?token=<jwt_token>`
- **Header:** `Authorization: Bearer <jwt_token>`

#### Client-to-Server Messages

**Ping:**
```json
{
  "type": "ping",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**Request App Data:**
```json
{
  "type": "request_app_data",
  "app_id": 123
}
```

**Update App Data:**
```json
{
  "type": "update_app_data",
  "data": {
    "components": [...],
    "layouts": [...],
    "styles": {...}
  }
}
```

#### Server-to-Client Messages

**Pong:**
```json
{
  "type": "pong",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**App Data:**
```json
{
  "type": "app_data",
  "data": {
    "components": [...],
    "layouts": [...],
    "styles": {...},
    "data": {...}
  }
}
```

**Error:**
```json
{
  "type": "error",
  "message": "Authentication required",
  "code": "AUTH_REQUIRED"
}
```

### Collaboration WebSocket

Real-time multi-user collaboration features.

#### Client-to-Server Messages

**Join Session:**
```json
{
  "type": "join_session",
  "session_id": "session_123",
  "user_info": {
    "username": "john_doe",
    "avatar": "https://example.com/avatar.jpg"
  }
}
```

**Cursor Update:**
```json
{
  "type": "cursor_update",
  "position": {
    "x": 150,
    "y": 200
  },
  "component_id": "button_1"
}
```

**Edit Operation:**
```json
{
  "type": "edit_operation",
  "operation": {
    "type": "component_update",
    "component_id": "button_1",
    "property": "text",
    "value": "New Text",
    "old_value": "Old Text"
  }
}
```

**Chat Message:**
```json
{
  "type": "chat_message",
  "message": "Hello everyone!"
}
```

**Create Comment:**
```json
{
  "type": "create_comment",
  "content": "This needs to be bigger",
  "component_id": "button_1",
  "canvas_position": {
    "x": 100,
    "y": 150
  }
}
```

#### Server-to-Client Messages

**User Joined:**
```json
{
  "type": "user_joined",
  "user": {
    "id": 123,
    "username": "jane_doe",
    "avatar": "https://example.com/avatar2.jpg"
  },
  "session_participants": [...]
}
```

**Cursor Broadcast:**
```json
{
  "type": "cursor_broadcast",
  "user_id": 123,
  "username": "jane_doe",
  "position": {
    "x": 150,
    "y": 200
  },
  "component_id": "button_1"
}
```

**Edit Broadcast:**
```json
{
  "type": "edit_broadcast",
  "user_id": 123,
  "operation": {
    "type": "component_update",
    "component_id": "button_1",
    "property": "text",
    "value": "New Text"
  }
}
```

**Chat Broadcast:**
```json
{
  "type": "chat_message",
  "user_id": 123,
  "username": "jane_doe",
  "message": "Hello everyone!",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### AI Suggestions WebSocket

Real-time AI-powered layout and component suggestions.

#### Client-to-Server Messages

**Get Layout Suggestions:**
```json
{
  "type": "get_layout_suggestions",
  "components": [...],
  "layouts": [...],
  "context": {
    "app_type": "business",
    "target_audience": "professionals"
  }
}
```

**Get Component Combinations:**
```json
{
  "type": "get_component_combinations",
  "components": [...],
  "selected_component": {
    "type": "Button",
    "id": "button_1"
  },
  "context": {...}
}
```

**Analyze App Structure:**
```json
{
  "type": "analyze_app_structure",
  "components": [...],
  "layouts": [...]
}
```

#### Server-to-Client Messages

**Layout Suggestions:**
```json
{
  "type": "layout_suggestions",
  "suggestions": [
    {
      "type": "grid",
      "description": "Three-column responsive grid",
      "confidence": 0.85,
      "layout": {...}
    }
  ],
  "component_count": 5,
  "status": "success"
}
```

**Component Combinations:**
```json
{
  "type": "component_combinations",
  "suggestions": [
    {
      "components": ["Button", "Input", "Label"],
      "description": "Form input group",
      "confidence": 0.92
    }
  ],
  "selected_component": "Button",
  "status": "success"
}
```

### Notifications WebSocket

Real-time notifications for users.

#### Client-to-Server Messages

**Mark as Read:**
```json
{
  "type": "mark_as_read",
  "notification_id": "notif_123"
}
```

**Get Notifications:**
```json
{
  "type": "get_notifications",
  "limit": 20,
  "offset": 0
}
```

#### Server-to-Client Messages

**New Notification:**
```json
{
  "type": "new_notification",
  "notification": {
    "id": "notif_456",
    "type": "mention",
    "message": "You were mentioned in a comment",
    "sender": "jane_doe",
    "read": false,
    "link": "/comments/123"
  }
}
```

**Unread Count:**
```json
{
  "type": "unread_count",
  "count": 5
}
```

### Performance Monitoring WebSocket

Real-time performance metrics and monitoring.

#### Client-to-Server Messages

**Start Monitoring:**
```json
{
  "type": "start_monitoring",
  "metrics": ["cpu", "memory", "network"],
  "interval": 5000
}
```

**Stop Monitoring:**
```json
{
  "type": "stop_monitoring"
}
```

**Get System Info:**
```json
{
  "type": "get_system_info"
}
```

#### Server-to-Client Messages

**Performance Metrics:**
```json
{
  "type": "performance_metrics",
  "metrics": {
    "cpu_usage": 45.2,
    "memory_usage": 67.8,
    "network_io": {
      "bytes_sent": 1024,
      "bytes_received": 2048
    }
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**System Info:**
```json
{
  "type": "system_info",
  "info": {
    "platform": "linux",
    "cpu_count": 4,
    "total_memory": 8589934592,
    "python_version": "3.9.7"
  }
}
```

## GraphQL API

The App Builder also provides a GraphQL endpoint for flexible data querying.

### Endpoint

```
POST /api/graphql/
Content-Type: application/json
Authorization: Bearer <token>
```

### GraphiQL Interface

Access the interactive GraphQL explorer at:
```
GET /api/graphql/
```

### Example Queries

**Get Apps:**
```graphql
query GetApps($first: Int, $after: String) {
  allApps(first: $first, after: $after) {
    edges {
      node {
        id
        name
        description
        createdAt
        updatedAt
        user {
          username
          email
        }
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}
```

**Get App Templates:**
```graphql
query GetAppTemplates($category: String) {
  allAppTemplates(appCategory: $category) {
    edges {
      node {
        id
        name
        description
        appCategory
        previewImage
        componentsJson
      }
    }
  }
}
```

### Mutations

**Create App:**
```graphql
mutation CreateApp($input: CreateAppInput!) {
  createApp(input: $input) {
    app {
      id
      name
      description
    }
    success
    errors
  }
}
```

## Security

### CORS Configuration

The API is configured with secure CORS settings:

**Allowed Origins:**
- `http://localhost:3000` (development)
- `http://127.0.0.1:3000` (development)
- Production domains (configured via environment variables)

**Allowed Methods:**
- GET, POST, PUT, PATCH, DELETE, OPTIONS

**Allowed Headers:**
- Authorization, Content-Type, X-CSRFToken, X-Requested-With

### Security Headers

All API responses include security headers:

```http
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
```

### Input Validation

All API endpoints implement comprehensive input validation:
- JSON schema validation
- SQL injection prevention
- XSS protection through input sanitization
- File upload restrictions

### Rate Limiting

Rate limiting is implemented to prevent abuse:
- Per-IP rate limiting
- Per-user rate limiting for authenticated endpoints
- Exponential backoff for repeated violations

## Development vs Production

### Development Environment

**Base URL:** `http://localhost:8000/api/`

**WebSocket URL:** `ws://localhost:8000/ws/`

**Features:**
- Debug mode enabled
- Detailed error messages
- CORS allows localhost origins
- SQLite database (default)

### Production Environment

**Base URL:** `https://your-domain.com/api/`

**WebSocket URL:** `wss://your-domain.com/ws/`

**Features:**
- Debug mode disabled
- Generic error messages
- Strict CORS configuration
- PostgreSQL database
- SSL/TLS encryption
- Security headers enforced

### Environment Variables

Key environment variables for configuration:

```bash
# Database
DATABASE_URL=postgresql://user:pass@localhost/dbname

# Security
SECRET_KEY=your-secret-key
JWT_SECRET_KEY=your-jwt-secret

# CORS
DJANGO_CORS_ALLOWED_ORIGINS=https://your-domain.com

# Features
OPENAI_API_KEY=your-openai-key
REDIS_URL=redis://localhost:6379

# Email
EMAIL_HOST=smtp.your-provider.com
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-password
```
## Integration Examples

### Frontend JavaScript Integration

#### Basic API Client

```javascript
class AppBuilderAPI {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`,
        ...options.headers
      },
      ...options
    };

    const response = await fetch(url, config);

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    return response.json();
  }

  // App management
  async getApps() {
    return this.request('/api/v1/apps/');
  }

  async createApp(appData) {
    return this.request('/api/v1/apps/', {
      method: 'POST',
      body: JSON.stringify(appData)
    });
  }

  async updateApp(id, appData) {
    return this.request(`/api/v1/apps/${id}/`, {
      method: 'PUT',
      body: JSON.stringify(appData)
    });
  }
}

// Usage
const api = new AppBuilderAPI('http://localhost:8000', 'your-jwt-token');

// Create a new app
const newApp = await api.createApp({
  name: 'My New App',
  description: 'A sample application',
  app_data: {
    components: [],
    layouts: []
  }
});
```

#### WebSocket Integration

```javascript
class AppBuilderWebSocket {
  constructor(url, token) {
    this.url = url;
    this.token = token;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  connect() {
    const wsUrl = `${this.url}?token=${this.token}`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
    };

    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      this.handleMessage(message);
    };

    this.ws.onclose = () => {
      console.log('WebSocket disconnected');
      this.reconnect();
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }

  send(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  handleMessage(message) {
    switch (message.type) {
      case 'app_data':
        this.onAppData(message.data);
        break;
      case 'error':
        this.onError(message);
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  }

  onAppData(data) {
    // Handle app data updates
    console.log('Received app data:', data);
  }

  onError(error) {
    console.error('WebSocket error:', error.message);
  }

  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, 1000 * this.reconnectAttempts);
    }
  }
}

// Usage
const ws = new AppBuilderWebSocket('ws://localhost:8000/ws/app_builder/', 'your-jwt-token');
ws.connect();

// Request app data
ws.send({
  type: 'request_app_data',
  app_id: 123
});
```
### Python Integration

```python
import requests
import websocket
import json
import threading

class AppBuilderClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.token = token
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        })

    def get_apps(self):
        response = self.session.get(f'{self.base_url}/api/v1/apps/')
        response.raise_for_status()
        return response.json()

    def create_app(self, app_data):
        response = self.session.post(
            f'{self.base_url}/api/v1/apps/',
            json=app_data
        )
        response.raise_for_status()
        return response.json()

    def get_templates(self, template_type='component'):
        endpoint = f'/api/v1/{template_type}-templates/'
        response = self.session.get(f'{self.base_url}{endpoint}')
        response.raise_for_status()
        return response.json()

# Usage
client = AppBuilderClient('http://localhost:8000', 'your-jwt-token')

# Get all apps
apps = client.get_apps()
print(f"Found {apps['count']} apps")

# Create a new app
new_app = client.create_app({
    'name': 'Python Created App',
    'description': 'Created via Python client',
    'app_data': {
        'components': [],
        'layouts': []
    }
})
```

### cURL Examples

#### Authentication

```bash
# Get CSRF token
curl -X GET http://localhost:8000/api/csrf-token/

# Login with JWT
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "your_username", "password": "your_password"}'

# Register new user
curl -X POST http://localhost:8000/api/auth/register/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "new_user",
    "email": "<EMAIL>",
    "password": "secure_password"
  }'
```

#### App Management

```bash
# Get apps
curl -X GET http://localhost:8000/api/v1/apps/ \
  -H "Authorization: Bearer your-jwt-token"

# Create app
curl -X POST http://localhost:8000/api/v1/apps/ \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "cURL Created App",
    "description": "Created via cURL",
    "app_data": {
      "components": [],
      "layouts": []
    }
  }'

# Update app
curl -X PUT http://localhost:8000/api/v1/apps/1/ \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated App Name",
    "description": "Updated description"
  }'
```

#### Template Management

```bash
# Get component templates
curl -X GET http://localhost:8000/api/v1/component-templates/ \
  -H "Authorization: Bearer your-jwt-token"

# Create component template
curl -X POST http://localhost:8000/api/v1/component-templates/ \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Custom Button",
    "description": "A reusable button component",
    "component_type": "Button",
    "default_props": {
      "text": "Click Me",
      "variant": "primary"
    }
  }'
```

---

This documentation covers the complete App Builder API. For additional support or questions, please refer to the project repository or contact the development team.
