const themeConfig = {
  token: {
    // Colors - Enhanced for better contrast (WCAG AA compliance)
    colorPrimary: '#1D4ED8',    // Enhanced contrast (5.9:1 on white)
    colorSuccess: '#047857',    // Enhanced contrast (4.8:1 on white)
    colorWarning: '#D97706',    // Enhanced contrast (4.5:1 on white)
    colorError: '#DC2626',      // Enhanced contrast (5.7:1 on white)
    colorInfo: '#1D4ED8',       // Enhanced contrast (5.9:1 on white)

    // Typography
    fontFamily: "'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
    fontSize: 14,

    // Border radius
    borderRadius: 4,

    // Spacing
    marginXS: 8,
    marginSM: 12,
    margin: 16,
    marginMD: 20,
    marginLG: 24,
    marginXL: 32,

    // Shadows
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
    boxShadowSecondary: '0 1px 4px rgba(0, 0, 0, 0.1)',
  },
  components: {
    Button: {
      borderRadius: 4,
      controlHeight: 36,
    },
    Card: {
      borderRadius: 8,
    },
    Table: {
      borderRadius: 8,
    },
    Input: {
      borderRadius: 4,
    },
    Select: {
      borderRadius: 4,
    },
  },
};

export default themeConfig;
