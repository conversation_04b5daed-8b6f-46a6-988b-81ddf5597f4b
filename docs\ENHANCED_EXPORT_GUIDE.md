# Enhanced Code Export Functionality

## Overview

The Enhanced Code Export functionality provides comprehensive code generation capabilities for the App Builder application. It supports multiple frameworks, languages, and export formats with advanced configuration options, quality assurance, and modern development practices.

## Features

### 🚀 Supported Export Formats

#### Frontend Frameworks
- **React** - Modern React with hooks, TypeScript support
- **Vue.js** - Vue 3 with Composition API and TypeScript
- **Angular** - Angular with TypeScript and modern features
- **Svelte** - Svelte with modern JavaScript/TypeScript
- **Next.js** - Next.js with SSR and modern React features
- **Nuxt.js** - Nuxt.js for Vue applications

#### Mobile Frameworks
- **React Native** - Cross-platform mobile development
- **Flutter** - Google's UI toolkit for mobile, web, and desktop
- **Ionic** - Hybrid mobile app development

#### Static Formats
- **HTML/CSS/JavaScript** - Vanilla web technologies
- **Bootstrap Templates** - Bootstrap-based responsive templates

#### Backend APIs
- **Express.js** - Node.js web application framework
- **Django REST Framework** - Python web framework
- **FastAPI** - Modern Python API framework

### 🎨 Style Framework Support

- **Styled Components** - CSS-in-JS with styled-components
- **Emotion** - CSS-in-JS with Emotion
- **Tailwind CSS** - Utility-first CSS framework
- **CSS Modules** - Scoped CSS with modules
- **Material-UI** - React Material Design components
- **Chakra UI** - Simple and modular React components
- **Bootstrap** - Popular CSS framework

### ⚙️ Advanced Configuration Options

#### Code Quality
- **TypeScript Support** - Generate TypeScript code with type definitions
- **Accessibility Features** - Include ARIA labels, roles, and accessibility attributes
- **ESLint Configuration** - Code linting and style enforcement
- **Prettier Integration** - Code formatting

#### Testing & Documentation
- **Unit Tests** - Generate test files for components
- **Storybook Stories** - Component documentation and testing
- **README Generation** - Comprehensive project documentation
- **API Documentation** - Automatic API documentation generation

#### Project Structure
- **Single File** - All code in one file for simple projects
- **Multi-File** - Organized file structure with separate components
- **Full Project** - Complete project with build configs and tooling

#### Development Tools
- **Build Configuration** - Webpack, Vite, or Parcel configuration
- **Package Management** - npm, yarn, or pnpm support
- **Environment Configuration** - Development and production settings
- **Docker Support** - Containerization with Dockerfile and docker-compose
- **CI/CD Pipelines** - GitHub Actions, GitLab CI templates

## Usage

### Frontend Integration

```javascript
import EnhancedCodeExporter from './components/enhanced/EnhancedCodeExporter';

// Use in your App Builder interface
<EnhancedCodeExporter />
```

### API Endpoints

#### Enhanced Export
```http
POST /api/enhanced-export/
Content-Type: application/json

{
  "app_id": 1,
  "format": "react",
  "options": {
    "typescript": true,
    "include_accessibility": true,
    "project_structure": "full-project",
    "style_framework": "styled-components",
    "include_tests": true,
    "include_docker": true
  }
}
```

#### Template Export
```http
POST /api/export-template/
Content-Type: application/json

{
  "template_id": 1,
  "template_type": "layout",
  "format": "vue",
  "options": {
    "typescript": true,
    "project_structure": "full-project"
  }
}
```

#### Batch Export
```http
POST /api/batch-export/
Content-Type: application/json

{
  "app_ids": [1, 2, 3],
  "format": "react",
  "options": {
    "project_structure": "multi-file"
  }
}
```

### Backend Integration

```python
from core.enhanced_code_generator import EnhancedCodeGenerator, ExportOptions, ExportFormat
from core.code_validator import CodeValidator

# Generate code
generator = EnhancedCodeGenerator()
options = ExportOptions(
    format=ExportFormat.REACT,
    typescript=True,
    include_accessibility=True
)

generated_code = generator.generate_code(app_data, options)

# Validate generated code
validator = CodeValidator()
validation_results = validator.validate_code(generated_code, 'typescript', 'react')
```

## Configuration Options

### Export Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `format` | string | 'react' | Target export format |
| `typescript` | boolean | false | Enable TypeScript support |
| `include_accessibility` | boolean | true | Include accessibility features |
| `include_tests` | boolean | false | Generate test files |
| `include_storybook` | boolean | false | Generate Storybook stories |
| `style_framework` | string | 'styled-components' | CSS framework to use |
| `state_management` | string | 'useState' | State management solution |
| `project_structure` | string | 'single-file' | Project organization |
| `bundler` | string | 'vite' | Build tool configuration |
| `package_manager` | string | 'npm' | Package manager |
| `include_docker` | boolean | false | Include Docker configuration |
| `include_ci_cd` | boolean | false | Include CI/CD pipeline |

### Style Framework Options

- `styled-components` - CSS-in-JS with styled-components
- `emotion` - CSS-in-JS with Emotion
- `tailwind` - Utility-first CSS framework
- `css-modules` - Scoped CSS with modules
- `material-ui` - Material Design components
- `chakra-ui` - Chakra UI components
- `bootstrap` - Bootstrap CSS framework

### State Management Options

- `useState` - React hooks (default)
- `redux` - Redux Toolkit
- `zustand` - Zustand state management
- `context` - React Context API

## Quality Assurance

### Code Validation

The export system includes comprehensive code validation:

- **Syntax Validation** - Ensures generated code has valid syntax
- **Framework Compliance** - Validates framework-specific patterns
- **Accessibility Checks** - Ensures accessibility best practices
- **Security Validation** - Checks for common security issues
- **Performance Optimization** - Validates performance best practices

### Testing

Comprehensive test suite includes:

- **Unit Tests** - Test individual components and functions
- **Integration Tests** - Test complete export pipeline
- **Validation Tests** - Test code quality assurance
- **Performance Tests** - Test export performance with large applications

## Best Practices

### Code Generation

1. **Use TypeScript** for better type safety and developer experience
2. **Enable Accessibility** to ensure inclusive applications
3. **Include Tests** for better code quality and maintainability
4. **Use Modern Frameworks** for better performance and features
5. **Follow Naming Conventions** for consistent code organization

### Project Structure

1. **Single File** - For simple prototypes and demos
2. **Multi-File** - For organized development projects
3. **Full Project** - For production-ready applications

### Performance

1. **Optimize Bundle Size** - Use tree shaking and code splitting
2. **Enable Caching** - Configure proper caching strategies
3. **Use Modern Build Tools** - Vite, Webpack 5, or Parcel 2
4. **Minimize Dependencies** - Only include necessary packages

## Troubleshooting

### Common Issues

#### Export Fails
- Check app data structure
- Verify export format compatibility
- Review validation errors

#### Generated Code Issues
- Run code validation
- Check framework-specific requirements
- Verify accessibility compliance

#### Performance Issues
- Reduce component complexity
- Use pagination for large apps
- Optimize export options

### Error Messages

| Error | Cause | Solution |
|-------|-------|----------|
| "Invalid export format" | Unsupported format | Use supported format |
| "Template not found" | Missing template | Check template ID |
| "Validation failed" | Code quality issues | Review validation results |
| "Export timeout" | Large application | Reduce complexity or use batch export |

## API Reference

### EnhancedCodeGenerator

```python
class EnhancedCodeGenerator:
    def generate_code(self, app_data: Dict, options: ExportOptions) -> Union[str, Dict[str, str]]
```

### CodeValidator

```python
class CodeValidator:
    def validate_code(self, code: str, language: str, framework: str = None) -> List[ValidationResult]
    def validate_project_structure(self, files: Dict[str, str], framework: str) -> List[ValidationResult]
```

### ExportTemplateService

```python
class ExportTemplateService:
    def export_app_with_templates(self, app_id: int, export_format: str, options: Dict) -> Dict
    def export_template_as_project(self, template_id: int, template_type: str, export_format: str, options: Dict) -> Dict
    def batch_export_apps(self, app_ids: List[int], export_format: str, options: Dict) -> bytes
```

## Contributing

### Adding New Export Formats

1. **Implement Generator Method** - Add format-specific generation logic
2. **Add Validation Rules** - Define validation rules for the format
3. **Create Tests** - Add comprehensive tests for the new format
4. **Update Documentation** - Document the new format and its options

### Improving Code Quality

1. **Add Validation Rules** - Enhance code quality checks
2. **Optimize Performance** - Improve generation speed
3. **Enhance Accessibility** - Add more accessibility features
4. **Update Dependencies** - Keep frameworks and tools current

## License

This enhanced export functionality is part of the App Builder project and follows the same licensing terms.
