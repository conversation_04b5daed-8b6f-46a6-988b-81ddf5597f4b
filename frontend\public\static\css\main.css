/* Main application styles */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
  color: #333;
}

/* Loading styles */
.app-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.app-loading-content {
  text-align: center;
  padding: 2rem;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.app-loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #2563EB;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.App {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.App-header {
  background-color: #2563EB;
  padding: 20px;
  color: white;
  text-align: center;
}

.App-main {
  flex: 1;
  padding: 20px;
}

.App-nav {
  margin-bottom: 20px;
}

.App-nav ul {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.App-nav li {
  padding: 0;
}

.App-nav a {
  display: block;
  padding: 12px 20px;
  color: #333;
  text-decoration: none;
  transition: background-color 0.2s;
}

.App-nav a:hover {
  background-color: #f0f0f0;
}

.App-footer {
  background-color: white;
  padding: 16px;
  text-align: center;
  border-top: 1px solid #eaeaea;
}

.offline-mode-banner {
  background-color: #fff3cd;
  color: #856404;
  padding: 10px;
  margin: 10px 0;
  border-radius: 4px;
  text-align: center;
}

/* Dashboard Layout Styles */
.dashboard-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.sidebar {
  width: 250px;
  background-color: #1a202c;
  color: #e2e8f0;
  transition: width 0.3s ease;
  overflow-y: auto;
}

.sidebar.collapsed {
  width: 80px;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  background-color: #f7fafc;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 64px;
  background-color: white;
  border-bottom: 1px solid #e2e8f0;
}

.nav-item {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.nav-item:hover {
  background-color: #2d3748;
}

.nav-item.active {
  background-color: #4a5568;
}

.nav-icon {
  margin-right: 12px;
  font-size: 18px;
}

/* Component Builder Styles */
.component-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.component-item {
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.component-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.component-preview {
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background-color: white;
}

/* WebSocket Manager Styles */
.websocket-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-list {
  height: 300px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 8px;
  background-color: white;
}

.message-item {
  padding: 8px;
  margin-bottom: 8px;
  border-radius: 4px;
}

.message-item.sent {
  background-color: #ebf8ff;
  align-self: flex-end;
}

.message-item.received {
  background-color: #f0fff4;
  align-self: flex-start;
}

.connection-status {
  display: flex;
  align-items: center;
  padding: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.connected {
  background-color: #48bb78;
}

.status-indicator.disconnected {
  background-color: #f56565;
}

/* Enhanced responsive styles */
@media (max-width: 768px) {
  .App-nav ul {
    flex-direction: column;
  }

  .component-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  /* Enhanced dashboard responsiveness */
  .dashboard-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  .sidebar {
    width: 100%;
    position: static;
    height: auto;
    order: 2;
  }

  .sidebar.collapsed {
    width: 100%;
    height: 60px;
  }

  .main-content {
    order: 1;
    padding: 12px;
  }

  .header {
    padding: 8px 12px;
    height: auto;
    min-height: 56px;
    flex-wrap: wrap;
  }

  .nav-item {
    padding: 8px 12px;
    justify-content: center;
    text-align: center;
  }

  .nav-icon {
    margin-right: 8px;
    font-size: 16px;
  }

  /* Component builder mobile improvements */
  .component-item {
    margin-bottom: 12px;
  }

  .component-preview {
    padding: 12px;
  }

  /* WebSocket container mobile improvements */
  .websocket-container {
    gap: 12px;
  }

  .message-list {
    height: 200px;
    padding: 12px;
  }

  .message-item {
    padding: 6px;
    margin-bottom: 6px;
    font-size: 14px;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .component-grid {
    gap: 8px;
  }

  .header {
    padding: 6px 8px;
    min-height: 48px;
  }

  .nav-item {
    padding: 6px 8px;
    font-size: 14px;
  }

  .nav-icon {
    font-size: 14px;
    margin-right: 6px;
  }

  .component-preview {
    padding: 8px;
  }

  .message-list {
    height: 150px;
    padding: 8px;
  }
}