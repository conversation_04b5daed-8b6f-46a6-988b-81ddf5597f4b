[pytest]
DJANGO_SETTINGS_MODULE = app_builder_201.test_settings
python_files = test_*.py
python_classes = Test*
python_functions = test_*
asyncio_mode = strict
addopts =
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --cov=.
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-report=term-missing
    --cov-fail-under=70
    --html=reports/pytest_report.html
    --self-contained-html
    --json-report
    --json-report-file=reports/pytest_report.json
    --maxfail=5
    --disable-warnings
testpaths = tests
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    security: Security tests
    performance: Performance tests
    websocket: WebSocket tests
    api: API tests
    models: Model tests
    views: View tests
    utils: Utility tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:django.*
    ignore::RuntimeWarning:channels.*
