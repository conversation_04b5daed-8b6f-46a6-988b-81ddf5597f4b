# Web framework
Django>=4.2.11,<5.0

# WebSocket support
channels>=4.0.0
daphne>=4.0.0
channels-redis>=4.1.0

# CORS headers
django-cors-headers>=4.3.1

# REST API
djangorestframework>=3.15.0
django-filter>=23.5

# GraphQL API
graphene-django>=3.2.0

# Redis cache
django-redis>=5.4.0

# JWT authentication
PyJWT>=2.8.0

# Database drivers
psycopg2-binary>=2.9.9  # PostgreSQL

# API and data processing
flask==2.3.3
requests==2.31.0
sqlalchemy==2.0.27
pandas==2.2.0
numpy==1.26.3
psutil>=5.9.5

# Testing
pytest==7.4.4
pytest-asyncio>=0.23.5
pytest-django>=4.7.0
pytest-cov>=4.1.0
pytest-mock>=3.12.0
pytest-xdist>=3.5.0
factory-boy>=3.3.0
faker>=22.0.0
coverage>=7.4.0
model-bakery>=1.17.0
pytest-html>=4.1.1
pytest-json-report>=1.5.0
bandit>=1.7.5
safety>=3.0.1

# AI integration
openai>=1.12.0

# Security
django-ratelimit>=4.1.0

# Static files
whitenoise>=6.6.0
