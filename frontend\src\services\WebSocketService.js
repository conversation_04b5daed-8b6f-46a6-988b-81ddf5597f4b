// Singleton WebSocket service with safe initialization
class WebSocketService {
  constructor() {
    this.socket = null;
    this.connected = false;
    this.connecting = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
    this.reconnectInterval = 2000;
    this.maxReconnectInterval = 30000;
    this.reconnectDecay = 1.5;
    this.eventListeners = {};
    this.offlineQueue = [];
    this.isReconnecting = false;
    this.isClosing = false;
    this.isSuspended = false;
    this.messageQueue = [];
    this.lastError = null;
    this.heartbeatInterval = 30000; // 30 seconds
    this.heartbeatTimeoutId = null;
    this.missedHeartbeats = 0;
    this.maxMissedHeartbeats = 3;
    this.connectionTimeoutId = null;
    this.debug = false;
    this.instance = null;
  }

  // Initialize with safe defaults
  init(options = {}) {
    this.options = {
      autoConnect: false,
      autoReconnect: true,
      reconnectInterval: 2000,
      maxReconnectAttempts: 10,
      debug: false,
      queueOfflineMessages: true,
      ...options
    };

    // Set safe defaults
    this.connected = false;
    this.connecting = false;
    this.debug = this.options.debug;

    if (this.options.autoConnect) {
      this.connect();
    }

    return this;
  }

  // Set reconnect options
  setReconnectOptions(options = {}) {
    this.maxReconnectAttempts = options.maxAttempts || this.maxReconnectAttempts;
    this.reconnectInterval = options.initialDelay || this.reconnectInterval;
    this.maxReconnectInterval = options.maxDelay || this.maxReconnectInterval;
    this.reconnectDecay = options.useExponentialBackoff ? 1.5 : 1;

    return this;
  }

  // Configure security options
  configureSecurityOptions(options = {}) {
    this.securityOptions = {
      validateMessages: true,
      sanitizeMessages: true,
      rateLimiting: {
        enabled: true,
        maxMessagesPerSecond: 20,
        burstSize: 50
      },
      ...options
    };

    return this;
  }

  // Configure performance options
  configurePerformanceOptions(options = {}) {
    this.performanceOptions = {
      compression: {
        enabled: true,
        threshold: 1024,
        level: 6
      },
      batchingEnabled: true,
      batchInterval: 50,
      maxBatchSize: 20,
      offlineQueueEnabled: true,
      offlineStorage: {
        enabled: true,
        persistKey: 'websocket_offline_queue'
      },
      ...options
    };

    return this;
  }

  // Safe connection check that never throws
  isConnected() {
    try {
      return this.socket !== null &&
        this.socket !== undefined &&
        this.socket.readyState === WebSocket.OPEN;
    } catch (error) {
      console.error('Error checking connection status:', error);
      return false;
    }
  }

  // Connect to WebSocket server
  connect(endpoint) {
    return new Promise((resolve, reject) => {
      if (this.isConnected()) {
        resolve(this);
        return;
      }

      if (this.connecting) {
        reject(new Error('Connection already in progress'));
        return;
      }

      this.connecting = true;

      // Determine WebSocket URL
      let url;
      if (endpoint) {
        this.endpoint = endpoint;
      }

      // Use current location if no URL is specified
      if (!this.url) {
        // Check if we're in development mode
        const isDev = process.env.NODE_ENV === 'development';

        // Use our simple WebSocket server with fallbacks
        if (isDev) {
          // Try multiple URLs for development
          const possibleUrls = [
            'ws://localhost:8000/ws',
            'ws://127.0.0.1:8000/ws',
            'ws://localhost:8765',
            'ws://backend:8000/ws'
          ];

          // Use the first URL as default
          url = possibleUrls[0];

          // Store fallback URLs
          this.fallbackUrls = possibleUrls.slice(1);

          if (this.debug) {
            console.log('Development mode: Using WebSocket server at:', url);
            console.log('Fallback URLs:', this.fallbackUrls);
          }
        } else {
          // Production default
          url = 'ws://localhost:8765';
        }
      } else {
        url = this.url;
      }

      if (this.debug) {
        console.log(`Connecting to WebSocket at ${url}`);
      }

      try {
        this.socket = new WebSocket(url);

        this.socket.onopen = (event) => {
          this.connected = true;
          this.connecting = false;
          this.reconnectAttempts = 0;

          if (this.debug) {
            console.log('WebSocket connection established');
          }

          // Start heartbeat
          this.startHeartbeat();

          // Process any queued messages
          this.processOfflineQueue();

          // Trigger event listeners
          this.triggerEvent('open', event);
          this.triggerEvent('connect', event);

          resolve(this);
        };

        this.socket.onclose = (event) => {
          this.connected = false;
          this.connecting = false;

          if (this.debug) {
            console.log(`WebSocket connection closed: ${event.code} ${event.reason}`);
          }

          // Stop heartbeat
          this.stopHeartbeat();

          // Trigger event listeners
          this.triggerEvent('close', event);
          this.triggerEvent('disconnect', event);

          // Attempt to reconnect if not explicitly closed and auto reconnect is enabled
          if (!this.isClosing && this.options.autoReconnect) {
            this.reconnect();
          }

          this.isClosing = false;
        };

        this.socket.onerror = (event) => {
          // Log detailed error information
          console.error('WebSocket error occurred:', event);

          if (this.debug) {
            console.log('WebSocket readyState:', this.socket.readyState);
            console.log('Connection URL:', url);
            console.log('Browser:', navigator.userAgent);
          }

          // Handle the error with recovery strategies
          const error = this._handleConnectionError(event, this.reconnectAttempts);

          // If still connecting, reject the promise
          if (this.connecting) {
            this.connecting = false;
            reject(error);
          }

          // Trigger error event with detailed information
          this.triggerEvent('error', {
            originalEvent: event,
            url: url,
            timestamp: Date.now(),
            reconnectAttempts: this.reconnectAttempts,
            error: error
          });
        };

        this.socket.onmessage = (event) => {
          let data = event.data;

          try {
            // Check if message is compressed
            if (typeof data === 'string' && data.startsWith('C')) {
              // Remove compression header
              const compressedData = data.substring(1);

              // Decompress the data
              data = this._decompressMessage(compressedData);
            }

            // Parse JSON data
            if (typeof data === 'string') {
              data = JSON.parse(data);
            }

            if (this.debug) {
              console.log('WebSocket message received:', data);
            }

            // Handle heartbeat response
            if (data.type === 'pong') {
              this.missedHeartbeats = 0;
              this.triggerEvent('pong', data);
              return;
            }

            // Handle batch messages
            if (data.type === 'batch' && Array.isArray(data.messages)) {
              // Process each message in the batch
              data.messages.forEach(message => {
                this.triggerEvent('message', message);

                // Trigger specific event type if available
                if (message.type) {
                  this.triggerEvent(message.type, message);
                }
              });

              // Trigger batch event
              this.triggerEvent('batch', data);
              return;
            }

            // Trigger event listeners
            this.triggerEvent('message', data);

            // Trigger specific event type if available
            if (data.type) {
              this.triggerEvent(data.type, data);
            }
          } catch (error) {
            // Handle message processing error
            this._handleMessageError(error, event.data);
          }
        };
      } catch (error) {
        this.connecting = false;
        this.lastError = error;

        if (this.debug) {
          console.error('Error creating WebSocket:', error);
        }

        reject(error);
      }
    });
  }

  // Disconnect from WebSocket server
  disconnect() {
    if (!this.socket) {
      return;
    }

    this.isClosing = true;

    try {
      this.stopHeartbeat();
      this.socket.close(1000, 'Normal closure');

      if (this.debug) {
        console.log('WebSocket disconnected');
      }
    } catch (error) {
      console.error('Error disconnecting WebSocket:', error);
    }
  }

  // Alias for disconnect
  close() {
    return this.disconnect();
  }

  // Reconnect to WebSocket server
  reconnect() {
    if (this.isReconnecting) {
      return;
    }

    this.isReconnecting = true;

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      if (this.debug) {
        console.log(`Maximum reconnect attempts (${this.maxReconnectAttempts}) reached`);
      }

      this.triggerEvent('max_retries_reached', {
        attempts: this.reconnectAttempts,
        max: this.maxReconnectAttempts
      });

      this.isReconnecting = false;
      return;
    }

    this.reconnectAttempts++;

    // Calculate reconnect delay with exponential backoff and enhanced jitter
    let exponentialDelay = this.reconnectInterval * Math.pow(this.reconnectDecay, this.reconnectAttempts - 1);
    exponentialDelay = Math.min(exponentialDelay, this.maxReconnectInterval);

    // Enhanced jitter implementation to prevent thundering herd problem
    // Use ±30% jitter with better distribution
    const jitterFactor = 0.3;
    const jitterRange = exponentialDelay * jitterFactor;
    const jitter = (Math.random() - 0.5) * 2 * jitterRange;

    // Ensure delay is positive and within reasonable bounds
    const delay = Math.max(this.reconnectInterval, Math.round(exponentialDelay + jitter));

    if (this.debug) {
      console.log(`Reconnecting in ${Math.round(delay / 1000)}s (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`, {
        exponentialDelay: Math.round(exponentialDelay),
        jitter: Math.round(jitter),
        finalDelay: delay,
        jitterFactor
      });
    }

    this.triggerEvent('reconnecting', {
      attempt: this.reconnectAttempts,
      maxAttempts: this.maxReconnectAttempts,
      delay
    });

    setTimeout(() => {
      this.isReconnecting = false;
      this.connect().catch(error => {
        console.error('Reconnection failed:', error);
      });
    }, delay);
  }

  // Suspend WebSocket connection (e.g., when page is hidden)
  suspend() {
    if (this.isSuspended) {
      return;
    }

    this.isSuspended = true;

    if (this.debug) {
      console.log('WebSocket connection suspended');
    }

    // Store connection state before suspending
    this.wasPreviouslyConnected = this.isConnected();

    // Disconnect but don't trigger reconnect
    if (this.socket) {
      this.isClosing = true;
      this.stopHeartbeat();
      this.socket.close(1000, 'Connection suspended');
    }

    this.triggerEvent('suspended', {
      timestamp: Date.now(),
      wasPreviouslyConnected: this.wasPreviouslyConnected
    });

    return this;
  }

  // Resume WebSocket connection (e.g., when page becomes visible again)
  resume() {
    if (!this.isSuspended) {
      return;
    }

    this.isSuspended = false;

    if (this.debug) {
      console.log('Resuming WebSocket connection');
    }

    // Reconnect if previously connected
    if (this.wasPreviouslyConnected) {
      this.reconnect();
    }

    this.triggerEvent('resumed', {
      timestamp: Date.now(),
      reconnecting: this.wasPreviouslyConnected
    });

    return this;
  }

  // Handle online event
  handleOnline() {
    if (this.debug) {
      console.log('Network is online');
    }

    // Reconnect if previously connected
    if (this.wasPreviouslyConnected) {
      this.reconnect();
    }

    this.triggerEvent('network_status_change', {
      status: 'online',
      timestamp: Date.now()
    });

    return this;
  }

  // Handle offline event
  handleOffline() {
    if (this.debug) {
      console.log('Network is offline');
    }

    // Store connection state
    this.wasPreviouslyConnected = this.isConnected();

    this.triggerEvent('network_status_change', {
      status: 'offline',
      timestamp: Date.now()
    });

    return this;
  }

  // Send a message to the WebSocket server
  send(message, options = {}) {
    return new Promise((resolve, reject) => {
      if (!this.isConnected()) {
        if (this.options.queueOfflineMessages) {
          this.queueMessage(message, options);
          resolve({ queued: true });
        } else {
          reject(new Error('WebSocket is not connected'));
        }
        return;
      }

      try {
        // Apply rate limiting if enabled
        if (this.securityOptions?.rateLimiting?.enabled) {
          const now = Date.now();

          // Initialize rate limiting state if not exists
          if (!this._rateLimitState) {
            this._rateLimitState = {
              messageCount: 0,
              windowStart: now,
              burstCount: 0,
              burstStart: now,
              queue: []
            };
          }

          const state = this._rateLimitState;
          const { maxMessagesPerSecond, burstSize } = this.securityOptions.rateLimiting;

          // Reset window if needed
          if (now - state.windowStart > 1000) {
            state.messageCount = 0;
            state.windowStart = now;
          }

          // Reset burst if needed
          if (now - state.burstStart > 100) {
            state.burstCount = 0;
            state.burstStart = now;
          }

          // Check if rate limit exceeded
          if (state.messageCount >= maxMessagesPerSecond || state.burstCount >= burstSize) {
            // Queue the message for later sending
            if (options.important) {
              // Important messages go to the front of the queue
              state.queue.unshift({ message, options, timestamp: now });
            } else {
              state.queue.push({ message, options, timestamp: now });
            }

            this.triggerEvent('rate_limited', {
              queueLength: state.queue.length,
              messageCount: state.messageCount,
              burstCount: state.burstCount
            });

            // Schedule processing of the queue
            if (!state.processingQueue) {
              state.processingQueue = true;
              setTimeout(() => this._processRateLimitQueue(), 100);
            }

            resolve({ queued: true, rateLimited: true });
            return;
          }

          // Update rate limiting counters
          state.messageCount++;
          state.burstCount++;
        }

        // Prepare the message
        let data = message;

        // Add timestamp if not present
        if (typeof data === 'object' && !data.timestamp) {
          data = { ...data, timestamp: Date.now() };
        }

        // Add message ID if not present
        if (typeof data === 'object' && !data.id) {
          data = { ...data, id: this._generateMessageId() };
        }

        // Validate message if enabled
        if (this.securityOptions?.validateMessages) {
          const validationResult = this._validateMessage(data);
          if (!validationResult.valid) {
            reject(new Error(`Invalid message: ${validationResult.reason}`));
            return;
          }
        }

        // Sanitize message if enabled
        if (this.securityOptions?.sanitizeMessages) {
          data = this._sanitizeMessage(data);
        }

        // Add authentication token if available
        if (this.securityOptions?.authToken) {
          data = { ...data, auth: this.securityOptions.authToken };
        }

        // Check if message should be batched
        if (this.performanceOptions?.batchingEnabled && !options.immediate) {
          this._addToBatch(data, options);
          resolve({ queued: true, batched: true });
          return;
        }

        // Check if message should be compressed
        let messageString;
        let compressed = false;

        if (this.performanceOptions?.compression?.enabled &&
          (options.compress || this.performanceOptions.compression.enabled)) {
          const jsonString = typeof data === 'string' ? data : JSON.stringify(data);

          // Only compress if message is larger than threshold
          if (jsonString.length > this.performanceOptions.compression.threshold) {
            try {
              messageString = this._compressMessage(jsonString);
              compressed = true;

              // Add compression header
              messageString = `C${messageString}`;
            } catch (error) {
              console.error('Error compressing message:', error);
              messageString = jsonString;
            }
          } else {
            messageString = jsonString;
          }
        } else {
          // Convert to JSON string if needed
          messageString = typeof data === 'string' ? data : JSON.stringify(data);
        }

        // Send the message
        this.socket.send(messageString);

        if (this.debug) {
          console.log(`WebSocket message sent${compressed ? ' (compressed)' : ''}:`, data);
        }

        // Trigger event
        this.triggerEvent('message_sent', {
          message: data,
          compressed,
          size: messageString.length
        });

        resolve({ sent: true, compressed });
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
        reject(error);
      }
    });
  }

  // Generate a unique message ID
  _generateMessageId() {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  // Validate a message
  _validateMessage(message) {
    // Basic validation
    if (!message) {
      return { valid: false, reason: 'Message is empty' };
    }

    if (typeof message !== 'object') {
      return { valid: false, reason: 'Message must be an object' };
    }

    if (!message.type) {
      return { valid: false, reason: 'Message must have a type' };
    }

    // All checks passed
    return { valid: true };
  }

  // Sanitize a message
  _sanitizeMessage(message) {
    // Create a deep copy to avoid modifying the original
    const sanitized = JSON.parse(JSON.stringify(message));

    // Sanitize string values to prevent XSS
    const sanitizeValue = (value) => {
      if (typeof value === 'string') {
        // Basic XSS protection
        return value
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&#x27;')
          .replace(/\//g, '&#x2F;');
      }
      return value;
    };

    // Recursively sanitize all string values
    const sanitizeObject = (obj) => {
      if (!obj || typeof obj !== 'object') return obj;

      if (Array.isArray(obj)) {
        return obj.map(item => sanitizeObject(item));
      }

      const result = {};
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          const value = obj[key];
          if (typeof value === 'object' && value !== null) {
            result[key] = sanitizeObject(value);
          } else {
            result[key] = sanitizeValue(value);
          }
        }
      }
      return result;
    };

    return sanitizeObject(sanitized);
  }

  // Compress a message
  _compressMessage(message) {
    // In a real implementation, this would use a compression algorithm
    // For this example, we'll use a simple Base64 encoding as a placeholder
    try {
      return btoa(message);
    } catch (error) {
      console.error('Error compressing message:', error);
      return message;
    }
  }

  // Decompress a message
  _decompressMessage(message) {
    // In a real implementation, this would use a decompression algorithm
    // For this example, we'll use a simple Base64 decoding as a placeholder
    try {
      return atob(message);
    } catch (error) {
      console.error('Error decompressing message:', error);
      return message;
    }
  }

  // Add a message to the batch
  _addToBatch(message, options) {
    // Initialize batch if not exists
    if (!this._batch) {
      this._batch = {
        messages: [],
        timer: null
      };
    }

    // Add message to batch
    this._batch.messages.push({ message, options });

    // Start batch timer if not already running
    if (!this._batch.timer) {
      this._batch.timer = setTimeout(() => {
        this._sendBatch();
      }, this.performanceOptions.batchInterval);
    }

    // Send batch immediately if it reaches the max size
    if (this._batch.messages.length >= this.performanceOptions.maxBatchSize) {
      clearTimeout(this._batch.timer);
      this._batch.timer = null;
      this._sendBatch();
    }
  }

  // Send a batch of messages
  _sendBatch() {
    if (!this._batch || this._batch.messages.length === 0) {
      return;
    }

    // Create batch message
    const batchMessage = {
      type: 'batch',
      messages: this._batch.messages.map(item => item.message),
      timestamp: Date.now(),
      count: this._batch.messages.length
    };

    // Clear batch
    const messages = this._batch.messages;
    this._batch.messages = [];
    this._batch.timer = null;

    // Send batch
    this.send(batchMessage, { immediate: true })
      .catch(error => {
        console.error('Error sending batch:', error);

        // Re-queue messages on error
        messages.forEach(item => {
          this.queueMessage(item.message, item.options);
        });
      });
  }

  // Process rate limit queue
  _processRateLimitQueue() {
    if (!this._rateLimitState || this._rateLimitState.queue.length === 0) {
      if (this._rateLimitState) {
        this._rateLimitState.processingQueue = false;
      }
      return;
    }

    const now = Date.now();
    const state = this._rateLimitState;
    const { maxMessagesPerSecond } = this.securityOptions.rateLimiting;

    // Reset window if needed
    if (now - state.windowStart > 1000) {
      state.messageCount = 0;
      state.windowStart = now;
    }

    // Check if we can send more messages
    if (state.messageCount < maxMessagesPerSecond) {
      // Get the next message
      const item = state.queue.shift();

      // Send the message
      this.send(item.message, { ...item.options, immediate: true })
        .catch(error => {
          console.error('Error sending queued message:', error);
        });

      // Update counter
      state.messageCount++;

      // Schedule next processing
      if (state.queue.length > 0) {
        setTimeout(() => this._processRateLimitQueue(), 50);
      } else {
        state.processingQueue = false;
      }
    } else {
      // Wait until we can send more messages
      setTimeout(() => this._processRateLimitQueue(), 100);
    }
  }

  // Alias for send
  sendMessage(message, options = {}) {
    return this.send(message, options);
  }

  // Queue a message to be sent when connection is established
  queueMessage(message, options = {}) {
    if (this.debug) {
      console.log('Queuing message for later delivery:', message);
    }

    this.offlineQueue.push({ message, options });

    // Trigger event
    this.triggerEvent('message_queued_offline', {
      message,
      queueLength: this.offlineQueue.length
    });

    return this.offlineQueue.length;
  }

  // Process queued messages
  processOfflineQueue() {
    if (this.offlineQueue.length === 0) {
      return;
    }

    if (this.debug) {
      console.log(`Processing offline queue (${this.offlineQueue.length} messages)`);
    }

    // Create a copy of the queue and clear it
    const queue = [...this.offlineQueue];
    this.offlineQueue = [];

    // Process each message
    queue.forEach(({ message, options }) => {
      this.send(message, options).catch(error => {
        console.error('Error sending queued message:', error);
      });
    });

    // Trigger event
    this.triggerEvent('offline_queue_processed', {
      processedCount: queue.length
    });
  }

  // Start heartbeat to keep connection alive
  startHeartbeat() {
    this.stopHeartbeat();

    this.missedHeartbeats = 0;

    this.heartbeatTimeoutId = setInterval(() => {
      if (!this.isConnected()) {
        this.stopHeartbeat();
        return;
      }

      this.missedHeartbeats++;

      if (this.missedHeartbeats >= this.maxMissedHeartbeats) {
        if (this.debug) {
          console.warn(`Missed ${this.missedHeartbeats} heartbeats, reconnecting...`);
        }

        this.triggerEvent('heartbeat_timeout', {
          missed: this.missedHeartbeats,
          max: this.maxMissedHeartbeats
        });

        this.reconnect();
        return;
      }

      // Send heartbeat
      this.send({ type: 'ping', timestamp: Date.now() })
        .catch(error => {
          console.error('Error sending heartbeat:', error);
        });

    }, this.heartbeatInterval);
  }

  // Stop heartbeat
  stopHeartbeat() {
    if (this.heartbeatTimeoutId) {
      clearInterval(this.heartbeatTimeoutId);
      this.heartbeatTimeoutId = null;
    }
  }

  // Add event listener
  addEventListener(event, callback) {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = [];
    }

    this.eventListeners[event].push(callback);

    return this;
  }

  // Remove event listener
  removeEventListener(event, callback) {
    if (!this.eventListeners[event]) {
      return this;
    }

    this.eventListeners[event] = this.eventListeners[event].filter(cb => cb !== callback);

    return this;
  }

  // Alias for addEventListener (for compatibility)
  on(event, callback) {
    return this.addEventListener(event, callback);
  }

  // Alias for removeEventListener (for compatibility)
  off(event, callback) {
    return this.removeEventListener(event, callback);
  }

  // Trigger event
  triggerEvent(event, data) {
    if (!this.eventListeners[event]) {
      return;
    }

    this.eventListeners[event].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in ${event} event listener:`, error);
      }
    });
  }

  // Get connection state
  getConnectionState() {
    let readyStateText = 'CLOSED';

    if (this.socket) {
      switch (this.socket.readyState) {
        case WebSocket.CONNECTING:
          readyStateText = 'CONNECTING';
          break;
        case WebSocket.OPEN:
          readyStateText = 'OPEN';
          break;
        case WebSocket.CLOSING:
          readyStateText = 'CLOSING';
          break;
        case WebSocket.CLOSED:
          readyStateText = 'CLOSED';
          break;
      }
    }

    return {
      connected: this.isConnected(),
      connecting: this.connecting,
      readyState: this.socket ? this.socket.readyState : WebSocket.CLOSED,
      readyStateText,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts,
      url: this.url,
      lastError: this.lastError
    };
  }

  // Get offline queue status
  getOfflineQueueStatus() {
    return {
      count: this.offlineQueue.length,
      enabled: this.options.queueOfflineMessages
    };
  }

  // Clear offline queue
  clearOfflineQueue() {
    const count = this.offlineQueue.length;
    this.offlineQueue = [];

    this.triggerEvent('offline_queue_cleared', { count });

    return count;
  }

  // Get singleton instance
  static getInstance(options) {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();

      if (options) {
        WebSocketService.instance.init(options);
      }
    }

    return WebSocketService.instance;
  }

  // Get default WebSocket URL
  _getDefaultWebSocketUrl() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    return `${protocol}//${host}/ws`;
  }

  // Create a specific error type
  _createError(type, message, details = {}) {
    const error = new Error(message);
    error.type = type;
    error.details = details;
    error.timestamp = Date.now();

    // Log error if debug is enabled
    if (this.debug) {
      console.error(`WebSocketError [${type}]: ${message}`, details);
    }

    // Trigger error event
    this.triggerEvent('error', error);

    return error;
  }

  // Handle connection errors with recovery strategies
  _handleConnectionError(error, attempt = 1) {
    // Store the error
    this.lastError = error;

    // Determine error type
    let errorType = 'CONNECTION_ERROR';
    let errorMessage = 'Failed to connect to WebSocket server';

    if (error.code) {
      switch (error.code) {
        case 1000:
          errorType = 'NORMAL_CLOSURE';
          errorMessage = 'Connection closed normally';
          break;
        case 1001:
          errorType = 'GOING_AWAY';
          errorMessage = 'Server is going away';
          break;
        case 1002:
          errorType = 'PROTOCOL_ERROR';
          errorMessage = 'Protocol error';
          break;
        case 1003:
          errorType = 'UNSUPPORTED_DATA';
          errorMessage = 'Unsupported data';
          break;
        case 1005:
          errorType = 'NO_STATUS';
          errorMessage = 'No status code was provided';
          break;
        case 1006:
          errorType = 'ABNORMAL_CLOSURE';
          errorMessage = 'Connection closed abnormally';
          break;
        case 1007:
          errorType = 'INVALID_FRAME_PAYLOAD';
          errorMessage = 'Invalid frame payload data';
          break;
        case 1008:
          errorType = 'POLICY_VIOLATION';
          errorMessage = 'Policy violation';
          break;
        case 1009:
          errorType = 'MESSAGE_TOO_BIG';
          errorMessage = 'Message too big';
          break;
        case 1010:
          errorType = 'MISSING_EXTENSION';
          errorMessage = 'Required extension is missing';
          break;
        case 1011:
          errorType = 'INTERNAL_ERROR';
          errorMessage = 'Internal server error';
          break;
        case 1012:
          errorType = 'SERVICE_RESTART';
          errorMessage = 'Service is restarting';
          break;
        case 1013:
          errorType = 'TRY_AGAIN_LATER';
          errorMessage = 'Try again later';
          break;
        case 1014:
          errorType = 'BAD_GATEWAY';
          errorMessage = 'Bad gateway';
          break;
        case 1015:
          errorType = 'TLS_HANDSHAKE_FAILURE';
          errorMessage = 'TLS handshake failure';
          break;
        default:
          errorType = `CODE_${error.code}`;
          errorMessage = `WebSocket error code ${error.code}`;
      }
    } else if (error.message) {
      if (error.message.includes('timeout')) {
        errorType = 'CONNECTION_TIMEOUT';
        errorMessage = 'Connection timed out';
      } else if (error.message.includes('refused')) {
        errorType = 'CONNECTION_REFUSED';
        errorMessage = 'Connection refused';
      } else if (error.message.includes('ENOTFOUND')) {
        errorType = 'HOST_NOT_FOUND';
        errorMessage = 'Host not found';
      }
    }

    // Create error object
    const wsError = this._createError(errorType, errorMessage, {
      originalError: error,
      attempt,
      url: this.url
    });

    // Apply recovery strategy based on error type
    switch (errorType) {
      case 'NORMAL_CLOSURE':
        // No recovery needed for normal closure
        break;

      case 'GOING_AWAY':
      case 'SERVICE_RESTART':
        // Server is restarting, wait a bit longer before reconnecting
        if (this.options.autoReconnect) {
          setTimeout(() => this.reconnect(), 5000);
        }
        break;

      case 'TRY_AGAIN_LATER':
        // Server is busy, wait a bit longer before reconnecting
        if (this.options.autoReconnect) {
          setTimeout(() => this.reconnect(), 10000);
        }
        break;

      case 'CONNECTION_REFUSED':
      case 'HOST_NOT_FOUND':
        // Server might be down, try alternative URL if available
        if (this.options.fallbackUrls && this.options.fallbackUrls.length > 0) {
          const fallbackUrl = this.options.fallbackUrls[attempt % this.options.fallbackUrls.length];

          if (this.debug) {
            console.log(`Trying fallback URL: ${fallbackUrl}`);
          }

          this.url = fallbackUrl;

          if (this.options.autoReconnect) {
            setTimeout(() => this.reconnect(), 1000);
          }
        } else if (this.options.autoReconnect) {
          // No fallback URLs, just reconnect to the same URL
          this.reconnect();
        }
        break;

      case 'ABNORMAL_CLOSURE':
      case 'INTERNAL_ERROR':
      case 'BAD_GATEWAY':
        // Server error, reconnect with exponential backoff
        if (this.options.autoReconnect) {
          const delay = Math.min(1000 * Math.pow(2, attempt), 30000);
          setTimeout(() => this.reconnect(), delay);
        }
        break;

      default:
        // For other errors, use the default reconnect strategy
        if (this.options.autoReconnect) {
          this.reconnect();
        }
    }

    return wsError;
  }

  // Handle message errors
  _handleMessageError(error, message) {
    // Determine error type
    let errorType = 'MESSAGE_ERROR';
    let errorMessage = 'Failed to process WebSocket message';

    if (error.message) {
      if (error.message.includes('JSON')) {
        errorType = 'INVALID_JSON';
        errorMessage = 'Invalid JSON in message';
      } else if (error.message.includes('timeout')) {
        errorType = 'MESSAGE_TIMEOUT';
        errorMessage = 'Message processing timed out';
      } else if (error.message.includes('rate limit')) {
        errorType = 'RATE_LIMITED';
        errorMessage = 'Rate limit exceeded';
      }
    }

    // Create error object
    return this._createError(errorType, errorMessage, {
      originalError: error,
      message
    });
  }
}

// Create and export a singleton instance that's always defined
const webSocketServiceInstance = new WebSocketService();
export default webSocketServiceInstance;

