import React from 'react';
import { Typography, Switch, Slider, Radio } from 'antd';
import styled from 'styled-components';

const { Title, Paragraph } = Typography;

const AccessibilityContainer = styled.div`
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
`;

const OptionContainer = styled.div`
  margin-bottom: 24px;
`;

const OptionLabel = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

// Component to adjust text size
export const TextSizeAdjuster = ({ value, onChange }) => {
  return (
    <OptionContainer>
      <OptionLabel>
        <Typography.Text strong>Text Size</Typography.Text>
        <Typography.Text>{value}%</Typography.Text>
      </OptionLabel>
      <Slider
        min={75}
        max={200}
        step={25}
        value={value}
        onChange={onChange}
        marks={{
          75: 'A',
          100: 'A',
          125: 'A',
          150: 'A',
          175: 'A',
          200: 'A',
        }}
      />
    </OptionContainer>
  );
};

// Component to toggle high contrast mode
export const ContrastToggle = ({ value, onChange }) => {
  return (
    <OptionContainer>
      <OptionLabel>
        <Typography.Text strong>High Contrast</Typography.Text>
        <Switch checked={value} onChange={onChange} />
      </OptionLabel>
      <Typography.Text type="secondary">
        Increases contrast for better readability
      </Typography.Text>
    </OptionContainer>
  );
};

// Component to toggle reduced motion
export const ReducedMotionToggle = ({ value, onChange }) => {
  return (
    <OptionContainer>
      <OptionLabel>
        <Typography.Text strong>Reduced Motion</Typography.Text>
        <Switch checked={value} onChange={onChange} />
      </OptionLabel>
      <Typography.Text type="secondary">
        Minimizes animations and transitions
      </Typography.Text>
    </OptionContainer>
  );
};

// Component to select color blind mode
export const ColorBlindModeSelector = ({ value, onChange }) => {
  return (
    <OptionContainer>
      <OptionLabel>
        <Typography.Text strong>Color Blind Mode</Typography.Text>
      </OptionLabel>
      <Radio.Group value={value} onChange={e => onChange(e.target.value)}>
        <Radio.Button value="none">None</Radio.Button>
        <Radio.Button value="protanopia">Protanopia</Radio.Button>
        <Radio.Button value="deuteranopia">Deuteranopia</Radio.Button>
        <Radio.Button value="tritanopia">Tritanopia</Radio.Button>
      </Radio.Group>
    </OptionContainer>
  );
};

// Enhanced contrast toggle component
const EnhancedContrastToggle = ({ value, onChange }) => {
  return (
    <SettingGroup>
      <SettingLabel>Enhanced Contrast</SettingLabel>
      <SettingDescription>
        Improves text contrast ratios for better readability (WCAG AA compliance)
      </SettingDescription>
      <Switch
        checked={value}
        onChange={onChange}
        checkedChildren="On"
        unCheckedChildren="Off"
      />
    </SettingGroup>
  );
};

// Main accessibility panel component
const AccessibilityPanel = ({
  textSize = 100,
  onTextSizeChange,
  highContrast = false,
  onHighContrastChange,
  enhancedContrast = false,
  onEnhancedContrastChange,
  reducedMotion = false,
  onReducedMotionChange,
  colorBlindMode = 'none',
  onColorBlindModeChange
}) => {
  return (
    <AccessibilityContainer>
      <Title level={3}>Accessibility Settings</Title>
      <Paragraph>
        Customize the appearance of the application to improve accessibility.
      </Paragraph>

      <TextSizeAdjuster value={textSize} onChange={onTextSizeChange} />
      <ContrastToggle value={highContrast} onChange={onHighContrastChange} />
      <EnhancedContrastToggle value={enhancedContrast} onChange={onEnhancedContrastChange} />
      <ReducedMotionToggle value={reducedMotion} onChange={onReducedMotionChange} />
      <ColorBlindModeSelector value={colorBlindMode} onChange={onColorBlindModeChange} />
    </AccessibilityContainer>
  );
};

export default AccessibilityPanel;
